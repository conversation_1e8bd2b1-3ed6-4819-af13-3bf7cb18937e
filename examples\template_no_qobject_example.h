/**
 * @file template_no_qobject_example.h
 * @brief SARibbonJson 模板类使用示例（无 Q_OBJECT）
 * 
 * 展示如何使用不带 Q_OBJECT 的模板类，以及如何在派生类中添加信号槽支持
 */

#ifndef TEMPLATE_NO_QOBJECT_EXAMPLE_H
#define TEMPLATE_NO_QOBJECT_EXAMPLE_H

#include "SARibbonMainWindowJson.h"
#include <QTimer>
#include <QDebug>

// 示例 1: 使用类型别名（推荐方式）
class StandardRibbonWindow : public SARibbonMainWindowJson
{
    Q_OBJECT  // 在派生类中添加 Q_OBJECT

public:
    StandardRibbonWindow(QWidget *parent = nullptr)
        : SARibbonMainWindowJson(parent)
    {
        initRibbonByJson("./config/ribbon.json");
        setupDemo();
    }

private slots:
    void onDemoControlMapping()
    {
        qDebug() << "=== 控件映射功能演示 ===";
        
        // 检查控件是否存在
        if (hasControl("sta_operate_newequipment")) {
            qDebug() << "找到控件：sta_operate_newequipment";
            
            // 获取控件
            auto toolButton = getRibbonToolButton("sta_operate_newequipment");
            if (toolButton) {
                qDebug() << "控件文本：" << toolButton->text();
                
                // 使用便捷方法操作控件
                enableControlByCmd("sta_operate_newequipment", false);
                qDebug() << "已禁用控件";
                
                // 延迟恢复
                QTimer::singleShot(2000, [this]() {
                    enableControlByCmd("sta_operate_newequipment", true);
                    qDebug() << "已恢复控件";
                });
            }
        }
    }

private:
    void setupDemo()
    {
        // 注册一个演示命令
        ActionManager::instance().registerCommand("demo_control_mapping", 
            [this](bool) { onDemoControlMapping(); });
    }
};

// 示例 2: 继承自不同基类
class CustomRibbonWindow : public SARibbonJson<QMainWindow>
{
    Q_OBJECT

private:
    SARibbonBar* m_ribbonBar;

public:
    CustomRibbonWindow(QWidget *parent = nullptr)
        : SARibbonJson<QMainWindow>(parent)
        , m_ribbonBar(nullptr)
    {
        setupRibbon();
        initRibbonByJson("./config/ribbon.json");
    }

    // 模板类需要的接口
    SARibbonBar* ribbonBar() const
    {
        return m_ribbonBar;
    }

protected:
    // 重写虚函数添加自定义逻辑
    virtual QAction* createAction(const QString& text, const QString& iconPath) override
    {
        QAction* action = SARibbonJson<QMainWindow>::createAction(text, iconPath);
        action->setStatusTip(QString("自定义提示: %1").arg(text));
        return action;
    }

    virtual bool processCustomControlType(const QString& type, const QJsonObject& actionObj, SARibbonPannel* pannel) override
    {
        if (type == "custom_label") {
            auto label = new QLabel(actionObj.value("title").toString());
            label->setStyleSheet("color: blue; font-weight: bold;");
            pannel->addLargeWidget(label);
            
            auto cmd = actionObj.value("cmd").toString();
            if (!cmd.isEmpty()) {
                registerControl(cmd, label);
            }
            
            return true;
        }
        
        return SARibbonJson<QMainWindow>::processCustomControlType(type, actionObj, pannel);
    }

private:
    void setupRibbon()
    {
        m_ribbonBar = new SARibbonBar(this);
        // 这里需要根据具体需求设置 Ribbon Bar 的布局
        // 例如：setCentralWidget(m_ribbonBar) 或添加到布局中
    }
};

// 示例 3: 多重继承
class MultiInheritanceBase
{
public:
    virtual void customMethod() = 0;
    virtual ~MultiInheritanceBase() = default;
};

class AdvancedRibbonWindow : public SARibbonMainWindowJson, public MultiInheritanceBase
{
    Q_OBJECT

public:
    AdvancedRibbonWindow(QWidget *parent = nullptr)
        : SARibbonMainWindowJson(parent)
    {
        initRibbonByJson("./config/ribbon.json");
    }

    // 实现多重继承的接口
    virtual void customMethod() override
    {
        qDebug() << "执行自定义方法";
        
        // 可以使用控件映射功能
        if (hasControl("sta_operate_newequipment")) {
            enableControlByCmd("sta_operate_newequipment", false);
        }
    }

signals:
    void controlMappingReady();

public slots:
    void onControlMappingTest()
    {
        customMethod();
        emit controlMappingReady();
    }
};

// 示例 4: 泛型函数使用
template<typename WindowType>
void testControlMapping(WindowType* window)
{
    static_assert(std::is_base_of_v<SARibbonJson<typename WindowType::BaseType>, WindowType>, 
                  "WindowType must inherit from SARibbonJson");
    
    // 测试控件映射功能
    if (window->hasControl("sta_operate_newequipment")) {
        auto button = window->template getControl<SARibbonToolButton>("sta_operate_newequipment");
        if (button) {
            qDebug() << "找到按钮：" << button->text();
        }
    }
}

// 使用示例
inline void demonstrateUsage()
{
    // 创建不同类型的窗口
    auto standardWindow = new StandardRibbonWindow();
    auto customWindow = new CustomRibbonWindow();
    auto advancedWindow = new AdvancedRibbonWindow();
    
    // 测试控件映射
    // testControlMapping(standardWindow);  // 需要定义 BaseType
    
    // 显示窗口
    standardWindow->show();
    customWindow->show();
    advancedWindow->show();
}

#endif // TEMPLATE_NO_QOBJECT_EXAMPLE_H
