/*
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-05-12 20:37:40
 * @LastEditors: zhai
 * @LastEditTime: 2025-05-16 21:57:42
 */
#include "uiwave.h"
#include "qboxlayout.h"

#include <QLabel>
#include <QToolBar>

#include <app/settings.h>
#include "plot_widget.h"



UiWave::UiWave(const WaveConfig& config, QWidget *parent)
    : QWidget{parent}
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_toolbar_pane.margins");
    if(margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto toolBar = new QToolBar();
    // mainLayout->addWidget(toolBar);

    QVBoxLayout *paneLayout = new QVBoxLayout();
    mainLayout->addLayout(paneLayout, 1);

    QVector<int> pane_margins = YamlConfigUI::instance().getArray<int>("ui_pane.margins");
    if(pane_margins.size() == 4)
    {
        paneLayout->setContentsMargins(pane_margins[0], pane_margins[1], pane_margins[2], pane_margins[3]);
    }

    toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    toolBar->addWidget(new QLabel(config.title));

    QWidget* blank = new QWidget();
    blank->setObjectName("Blank");
    blank->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    toolBar->addWidget(blank);

    // toolBar->addAction(QIcon("./res/icon/Open.png"), "打开");
    // toolBar->addAction(QIcon("./res/icon/Open.png"), "打开");
    // toolBar->addSeparator();

    toolBar->addAction( "继续滚屏");
    toolBar->addSeparator();
    toolBar->addAction( "光标列表");
    toolBar->addAction( "标记列表");
    toolBar->addSeparator();

    toolBar->addAction(QIcon("./res/icon/widget/curve1.png"), "");
    toolBar->addAction(QIcon("./res/icon/widget/curve2.png"), "");
    toolBar->addAction(QIcon("./res/icon/widget/curve3.png"), "");
    toolBar->addAction(QIcon("./res/icon/widget/curve4.png"), "");
    toolBar->addAction(QIcon("./res/icon/widget/export.png"), "");
    toolBar->addSeparator();
    toolBar->addAction(QIcon("./res/icon/widget/scale.png"), "");

    customPlot = new PlotWidget(this);
    paneLayout->addWidget(customPlot);

    if (!config.xAxis.isEmpty())
    {
        customPlot->getPlot()->setFormatterTextX(config.xAxis);
    }
    if (!config.yAxis.isEmpty())
    {
        customPlot->getPlot()->setFormatterTextY(config.yAxis);
    }

    if( !config.xAxis.isEmpty() || !config.yAxis.isEmpty())
    {
        customPlot->getPlot()->updateTexts();
    }
}


void UiWave::realtimeDataSlot()
{
    static QTime timeStart = QTime::currentTime();
    // calculate two new data points:
    double key = timeStart.msecsTo(QTime::currentTime())/1000.0; // time elapsed since start of demo, in seconds
    static double lastPointKey = 0;
    if (key-lastPointKey > 0.002) // at most add point every 2 ms
    {
        // add data to lines:
        // customPlot->graph(0)->addData(key, qSin(key)+std::rand()/(double)RAND_MAX*1*qSin(key/0.3843));
        // customPlot->graph(1)->addData(key, qCos(key)+std::rand()/(double)RAND_MAX*0.5*qSin(key/0.4364));
        // rescale value (vertical) axis to fit the current data:
        //customPlot->graph(0)->rescaleValueAxis();
        //customPlot->graph(1)->rescaleValueAxis(true);
        lastPointKey = key;
    }
    // make key axis range scroll with the data (at a constant range size of 8):
    // customPlot->xAxis->setRange(key, 8, Qt::AlignRight);
    // customPlot->replot();

    // calculate frames per second:
    // static double lastFpsKey;
    // static int frameCount;
    // ++frameCount;
    // if (key-lastFpsKey > 2) // average fps over 2 seconds
    // {
    //     // ui->statusBar->showMessage(
    //     //     QString("%1 FPS, Total Data points: %2")
    //     //         .arg(frameCount/(key-lastFpsKey), 0, 'f', 0)
    //     //         .arg(ui->customPlot->graph(0)->data()->size()+ui->customPlot->graph(1)->data()->size())
    //     //     , 0);
    //     lastFpsKey = key;
    //     frameCount = 0;
    // }
}
