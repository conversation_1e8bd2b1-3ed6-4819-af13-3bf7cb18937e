#ifndef CODEWIDGET_H
#define CODEWIDGET_H
#include <QMap>
#include <QWidget>

class QsciScintilla;
class QsciLexerCPP;
class QTabWidget;
class QColor;
class QsciLexerPython;
class QsciLexerD;


class PythonFunctionCallHighlighter : public QObject {
    Q_OBJECT

public:
    explicit PythonFunctionCallHighlighter(QsciScintilla *editor, QObject *parent = nullptr);

    void setEnabled(bool enabled);

private:
    QsciScintilla *m_editor;
    QsciLexerPython *m_lexer;
    bool m_enabled = true;
    int m_indicatorIndex = 0;

    void initIndicator();
    void highlightCalls();
    int charPosToByteOffset(const QString &text, int charPos);

private slots:
    void scheduleHighlight();
};


class CodeWidget : public QWidget
{
    Q_OBJECT
public:
    CodeWidget(QWidget *parent = nullptr);
    ~CodeWidget();

    void InitLexer();
    void InitTextEdit();
    void ModifyLexerColor(int WordType, QColor &col);
    void ResortDefault();
    void CloseLineTag(bool flag);

    void setCurLexer(QString &type);

    QsciScintilla *m_scintilla;
    QsciLexerPython *m_py_lexer;
    QsciLexerCPP *m_cpp_lexer;
    QsciLexerD *m_default_lexer;
    QString m_cur_file;

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;

public slots:
    void highlightCurrentLine(int line, int index);
    void onTextChanged();
    void findNext(const QString &text, bool caseSensitive, bool wholeWords, bool forward = true);
    void selectAllText(const QString &text);
    void highLightAll(const QString &text);
    void replaceText(const QString &origin, const QString &replaced);
    void replaceAll(const QString &origin, const QString &replaced);

signals:

private:
    QMap<int, QColor> createDefaultColorMap();

private:
    enum
    {
        SymMargin1 = 0,
        SymMargin2 = 1,
        SymMargin3 = 3,
        LineNumMargin = 2,
        FoldMargin = 4
    };

    bool LineTagIsOpen = true;
    QMap<int, QColor> MapLexerColor;
    QColor CurrentLineTagColor = QColor(241, 196, 15);

    QColor PaperColor = QColor(30, 30, 30);
    QColor MarginBackColor = QColor(39, 39, 39);
    QColor MarginForeColor = QColor(204, 204, 204);

    QColor LineHighLightColor = QColor(46, 47, 48);
    QColor CursorColor = Qt::white;

    // rgb(212, 212, 212)
    QColor DefaultTextColor = QColor(212, 212, 212);

    // rgb(255, 165, 0)
    QColor MatchedBraceBackgroundColor = QColor(255, 165, 0, 60);
    
    // rgb(255, 0, 0)
    QColor UnmatchedBraceBackgroundColor = QColor(255, 0, 0, 30);

    // rgb(81, 80, 79)
    QColor MatchedBraceForegroundColor = QColor(81, 80, 79);

    int m_flags;
    int m_lastFindPos;
    int highlightIndicator = 7; // 需要初始化指示器ID

    QVector<QPair<int, int>> matchPosVec;
};

#endif // CODEWIDGET_H
