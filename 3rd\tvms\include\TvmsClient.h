#ifndef TVMSCLIENT_H
#define TVMSCLIENT_H

/*
** =============================================================================
** Predefined Macros
** =============================================================================
*/

#if defined(__cplusplus)
#define TVMSCLIENT_EXTERN           extern "C"
#else
#define TVMSCLIENT_EXTERN           extern
#endif


#if defined(TVMSCLIENT_BUILDING)
#define TVMSCLIENT_API              TVMSCLIENT_EXTERN __declspec(dllexport)
#else
#define TVMSCLIENT_API              TVMSCLIENT_EXTERN __declspec(dllimport)
#endif

/*
** =============================================================================
** Types
** =============================================================================
*/

/* 此类型的返回值为 1 表示操作成功, 否则值为 0. */
typedef unsigned char               TvmsBool_t;


/* 后台服务类别. */
typedef enum {
    TvmsScanElecSignal,     /* 电气信号采集服务 */
    TvmsScanGearPulses,     /* 齿轮脉冲采集服务 */
    TvmsThermals            /* 热力参数采集服务 */
} TvmsServicesKind;

/*
** 后台服务数据更新回调函数. data 是 Json 格式数据, 不需要释放.
** 注意此函数超过 200ms 不返回可能会导致数据不连续, 建议内存拷贝到缓冲区就返回.
*/
typedef void (*TvmsStatusCallBack_t)(
    TvmsServicesKind kind, const char *data, unsigned int size
);

/*
** =============================================================================
** Miscellaneous
** =============================================================================
*/

/* 释放由此动态库分配的内存. */
TVMSCLIENT_API
void TvmsFree(void *memory);

/*
** =============================================================================
** Services
** =============================================================================
*/

/*
** 获取服务 SQLite 配置文件路径, 如果服务没有安装返回 NULL.
** 此函数返回的指针需调用 'TvmsFree()' 释放.
*/
TVMSCLIENT_API
char *TvmsConfigFileName();


/* 启动数据采集服务, 此函数需要管理员权限. */
TVMSCLIENT_API
TvmsBool_t TvmsAcquisStart();

/* 停止数据采集服务, 此函数需要管理员权限. */
TVMSCLIENT_API
TvmsBool_t TvmsAcquisStop();


/*
** 设置后台服务数据更新回调函数.
** 此函数会启动一个线程查询服务数据, callback 为 NULL 时删除回调并停止线程.
*/
TVMSCLIENT_API
TvmsBool_t TvmsSetStatusCallback(TvmsStatusCallBack_t callback);


#endif  /* TVMSCLIENT_H */
