# Ribbon 控件映射功能

## 概述

基于 `SARibbonMainWindowJson` 基类的 Ribbon 控件映射系统，在 `initRibbonByJson` 过程中自动建立 cmd 和对应控件的映射关系，提供根据 cmd 查询控件的泛型功能。

## 架构设计

### 类层次结构
```
SARibbonMainWindow (第三方库)
    ↓
SARibbonMainWindowJson (新增基类)
    ↓
MainWindow (应用程序主窗口)
```

### 职责分离
- **SARibbonMainWindowJson**: 封装 JSON 配置解析和控件映射功能
- **MainWindow**: 专注于应用程序特定的业务逻辑

## 功能特性

- **泛型支持**：支持不同类型的控件映射
- **自动注册**：在创建控件时自动注册到映射中
- **类型安全**：使用模板函数确保类型安全
- **高效查询**：基于 QMap 的快速查询

## API 接口

### 注册控件
```cpp
template<typename T>
void registerControl(const QString& cmd, T* control);
```

### 查询控件
```cpp
template<typename T>
T* getControl(const QString& cmd) const;
```

### 检查控件是否存在
```cpp
bool hasControl(const QString& cmd) const;
```

### 便捷方法
```cpp
// 获取 RibbonToolButton
SARibbonToolButton* getRibbonToolButton(const QString& cmd) const;

// 控件操作便捷方法
void enableControlByCmd(const QString& cmd, bool enabled = true);
void setControlVisibleByCmd(const QString& cmd, bool visible = true);
```

## 使用示例

### 1. 继承基类
```cpp
// 在你的主窗口类中继承 SARibbonMainWindowJson
class MainWindow : public SARibbonMainWindowJson
{
    Q_OBJECT
public:
    MainWindow(QWidget *parent = nullptr);

protected:
    // 可选：重写 createAction 方法自定义 Action 创建
    virtual QAction* createAction(const QString& text, const QString& iconPath) override;

    // 可选：重写 processCustomControlType 方法支持自定义控件类型
    virtual bool processCustomControlType(const QString& type, const QJsonObject& actionObj, SARibbonPannel* pannel) override;
};
```

### 2. 初始化 Ribbon
```cpp
// 在构造函数或初始化方法中调用
void MainWindow::initRibbonBar()
{
    // ... 其他初始化代码 ...

    // 从 JSON 文件初始化 Ribbon（自动建立控件映射）
    initRibbonByJson("./config/ribbon.json");

    // ... 其他初始化代码 ...
}
```

### 3. 基本查询
```cpp
// 检查控件是否存在
if (hasControl("sta_operate_newequipment")) {
    // 获取特定类型的控件
    auto toolButton = getControl<SARibbonToolButton>("sta_operate_newequipment");
    if (toolButton) {
        toolButton->setEnabled(false);
        qDebug() << "控件文本：" << toolButton->text();
    }
}
```

### 4. 使用便捷方法
```cpp
// 直接获取控件
auto toolButton = getRibbonToolButton("sta_operate_newequipment");
if (toolButton) {
    toolButton->setStyleSheet("background-color: red;");
}

// 使用操作便捷方法
enableControlByCmd("sta_operate_newequipment", false);  // 禁用控件
setControlVisibleByCmd("sta_operate_importfile", false); // 隐藏控件
```

### 3. 泛型使用
```cpp
// 如果有其他类型的控件，比如 QWidget
auto widget = getControl<QWidget>("some_widget_cmd");
if (widget) {
    widget->setVisible(false);
}
```

## 实现原理

1. **存储机制**：使用 `QMap<QString, QVariant>` 存储 cmd 到控件指针的映射
2. **类型转换**：通过 `QVariant::fromValue` 和 `QVariant::value<void*>` 进行类型转换
3. **自动注册**：在 `initRibbonByJson` 中创建控件后自动调用 `registerControl`

## 注意事项

1. **生命周期**：确保控件的生命周期长于映射的使用期
2. **类型安全**：使用正确的模板参数类型进行查询
3. **空指针检查**：始终检查返回的指针是否为空
4. **cmd 唯一性**：确保每个 cmd 在系统中是唯一的

## 扩展支持

当前支持的控件类型：
- `SARibbonToolButton`（通过 `addLargeAction`/`addSmallAction` 创建）
- 可扩展支持其他 Ribbon 控件类型

如需支持新的控件类型，只需在创建控件后调用 `registerControl` 即可。
