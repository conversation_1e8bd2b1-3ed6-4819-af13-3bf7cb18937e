#include "qsplitter.h"
#include "uimonitorstatus.h"
#include "ui/monitor/uidatatable.h"
#include "ui/monitor/monitor_steam.h"
#include "ui/monitor/uiwave.h"
#include "plot_widget.h"
#include "core/Graph.h"
#include "utils/kissfft/kiss_fft.h"

#include <MainWindow.h>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QFrame>
#include <QPainter>
#include <QPaintEvent>
#include <QEnterEvent>
#include <QRandomGenerator>
#include <QVector>
#include <QPair>
#include "utils/fft.h"
#include "widgets/flowlayout.h"


#include <QGraphicsDropShadowEffect>
#include <QLocale>
#include <app/settings.h>
#include <yaml-cpp/yaml.h>

InfoCard::InfoCard(const QString &name, const QString &code, QWidget *parent)
    : Q<PERSON>rame(parent), m_unit("")
{
    setupUI();
    m_nameLabel->setText(name);
    m_codeLabel->setText("编码: " + code);
}

void InfoCard::setupUI()
{
    // 基础设置 - 更灵活的尺寸
    this->setMinimumSize(170, 110); 
    this->setFrameShape(QFrame::StyledPanel);
    
    // 深色主题QSS样式 - 适配#2B2B2B背景
    this->setStyleSheet(R"(
        InfoCard {
            background-color: #363636;
            border-radius: 5px;
            border: 1px solid #444;
        }
        InfoCard:hover {
            background-color: #3E3E3E;
            border: 1px solid #555;
        }
    )");

    // 阴影效果调整
    auto *shadowEffect = new QGraphicsDropShadowEffect(this);
    shadowEffect->setBlurRadius(10); // 减小阴影
    shadowEffect->setXOffset(0);
    shadowEffect->setYOffset(2);
    shadowEffect->setColor(QColor(10, 10, 10, 100));
    this->setGraphicsEffect(shadowEffect);

    // 控件样式 - 适配深色背景，字体大小根据卡片大小调整
    m_nameLabel = new QLabel(this);
    m_nameLabel->setAlignment(Qt::AlignCenter);
    m_nameLabel->setStyleSheet(R"(
        font-weight: bold; 
        font-size: 14px; 
        color: #E1E1E1;
    )");

    m_codeLabel = new QLabel(this);
    m_codeLabel->setAlignment(Qt::AlignCenter);
    m_codeLabel->setStyleSheet(R"(
        font-size: 11px; 
        color: #A0A0A0;
        margin-bottom: 5px;
    )");

    // 分隔线 - 更柔和的颜色
    QFrame *line = new QFrame(this);
    line->setFrameShape(QFrame::HLine);
    line->setFrameShadow(QFrame::Sunken);
    line->setStyleSheet("color: #555;");

    // 数值显示 - 使用更醒目的青蓝色
    m_valueLabel = new QLabel(this);
    m_valueLabel->setAlignment(Qt::AlignCenter);
    m_valueLabel->setStyleSheet(R"(
        font-weight: bold; 
        font-size: 22px; 
        color: #4FC1E9;
    )");

    m_unitLabel = new QLabel(this);
    m_unitLabel->setAlignment(Qt::AlignCenter);
    m_unitLabel->setStyleSheet(R"(
        font-size: 12px; 
        color: #A0A0A0;
    )");

    // 布局保持不变，但减小间距
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(6);
    mainLayout->setContentsMargins(8, 8, 8, 8);

    mainLayout->addWidget(m_nameLabel);
    mainLayout->addWidget(m_codeLabel);
    mainLayout->addWidget(line);
    
    QHBoxLayout *valueLayout = new QHBoxLayout();
    valueLayout->addStretch();
    valueLayout->addWidget(m_valueLabel);
    valueLayout->addWidget(m_unitLabel);
    valueLayout->addStretch();
    
    mainLayout->addLayout(valueLayout);
    mainLayout->addStretch();
}

void InfoCard::updateValue(double value)
{
    m_valueLabel->setText(formatValue(value));
    if (!m_unit.isEmpty()) {
        m_unitLabel->setText("(" + m_unit + ")");
    }
}

void InfoCard::setUnit(const QString &unit)
{
    m_unit = unit;
    m_unitLabel->setText("(" + m_unit + ")");
}

QString InfoCard::formatValue(double value) const
{
    QLocale locale;
    if (value == floor(value)) {
        return locale.toString(value, 'f', 0);
    }
    return locale.toString(value, 'f', 2);
}


UiMonitorSteam::UiMonitorSteam(const QString &id, QWidget *parent)
    : QWidget{parent}
{
    // 主布局 - 垂直布局
    auto mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(10, 10, 10, 10);
    mainLayout->setSpacing(0);

    // 创建垂直分隔器
    auto splitter = new QSplitter(Qt::Vertical, this);
    splitter->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(splitter);

    // 从YAML配置文件读取蒸汽参数组
    auto &yamlConfig = YamlConfigNode::instance();
    const YAML::Node& rootNode = yamlConfig.getRootNode();
    
    // 检查根节点是否有效
    if (!rootNode["Node"] || !rootNode["Node"]["Groups"]) {
        qWarning() << "YAML配置中找不到Node.Groups节点";
        return;
    }
    
    // 获取组节点
    const YAML::Node& groupsNode = rootNode["Node"]["Groups"];
    if (!groupsNode.IsSequence()) {
        qWarning() << "Node.Groups不是一个序列";
        return;
    }
    
    // 获取组的数量
    int groupCount = groupsNode.size();
    qDebug() << "找到组数量:" << groupCount;

    // 遍历配置中的组
    for (int i = 0; i < groupCount; i++) {
        const YAML::Node& groupNode = groupsNode[i];
        if (!groupNode.IsMap()) {
            qWarning() << "组" << i << "不是一个映射";
            continue;
        }
        
        // 获取组名
        if (!groupNode["Name"]) {
            qWarning() << "组" << i << "没有Name属性";
            continue;
        }
        QString groupName = QString::fromStdString(groupNode["Name"].as<std::string>());
        
        // 获取该组下的所有测点
        if (!groupNode["Points"] || !groupNode["Points"].IsSequence()) {
            qWarning() << "组" << i << "的Points不是一个序列";
            continue;
        }
        const YAML::Node& pointsNode = groupNode["Points"];
        int pointCount = pointsNode.size();
        
        // 创建该组的Widget
        QString defaultUnit = groupName.contains("压力") ? "kPa" : "°C";
        auto sectionWidget = createSectionWidget(groupName, defaultUnit, pointsNode);
        splitter->addWidget(sectionWidget);
    }

    // 设置分隔器比例 (均分)
    QList<int> sizes;
    for (int i = 0; i < splitter->count(); i++) {
        sizes.append(1);
    }
    splitter->setSizes(sizes);

    // 不允许折叠
    for (int i = 0; i < splitter->count(); i++) {
        splitter->setCollapsible(i, false);
    }
}

QWidget* UiMonitorSteam::createSectionWidget(const QString& title, const QString& defaultUnit, 
                                           const YAML::Node& pointsNode)
{
    // 创建主容器
    auto sectionWidget = new QWidget();
    auto sectionLayout = new QHBoxLayout(sectionWidget);
    sectionLayout->setContentsMargins(10, 10, 10, 10);
    sectionLayout->setSpacing(10);

    // 左侧标签
    auto titleLabel = new QLabel(title);
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setFixedWidth(120);
    titleLabel->setStyleSheet(R"(
        QLabel {
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
            background-color: #363636;
            border: 1px solid #555;
            border-radius: 5px;
            padding: 5px 10px;
            margin: 0px;
        }
        QLabel:hover {
            background-color: #3E3E3E;
            border-color: #666;
        }
    )");
    sectionLayout->addWidget(titleLabel);

    // 右侧卡片容器
    auto cardContainer = new QWidget();
    auto flowLayout = new FlowLayout(cardContainer, 10, 10, 10);
    flowLayout->setContentsMargins(0, 0, 0, 0);

    // 生成卡片
    int pointCount = pointsNode.size();
    for (int i = 0; i < pointCount; ++i) {
        const YAML::Node& pointNode = pointsNode[i];
        if (!pointNode.IsMap()) {
            qWarning() << "测点" << i << "不是一个映射";
            continue;
        }
        
        // 获取测点属性
        QString pn = pointNode["PN"] ? QString::fromStdString(pointNode["PN"].as<std::string>()) : "";
        QString an = pointNode["AN"] ? QString::fromStdString(pointNode["AN"].as<std::string>()) : "未命名测点";
        QString unit = pointNode["Unit"] ? QString::fromStdString(pointNode["Unit"].as<std::string>()) : defaultUnit;
        
        auto card = new InfoCard(an, pn);
        card->setUnit(unit);
        
        // 生成合理范围的随机值
        double randomValue;
        if (unit == "kPa") {
            // 压力：100-500 kPa
            randomValue = 100.0 + QRandomGenerator::global()->generateDouble() * 400.0;
        } else {
            // 温度：80-200 °C
            randomValue = 80.0 + QRandomGenerator::global()->generateDouble() * 120.0;
        }
        card->updateValue(randomValue);
        
        flowLayout->addWidget(card);
    }

    sectionLayout->addWidget(cardContainer, 1); // 让卡片容器占据剩余空间
    return sectionWidget;
}

bool UiMonitorSteam::isUiDataVisiable() const
{
    // return m_uiDataTable->isVisible();
    return true;
}

void UiMonitorSteam::setUiDataVisiable(bool visible)
{
    // m_uiDataTable->setVisible(visible);
}

void UiMonitorSteam::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 设置最小尺寸
    // m_splitter_top->setMinimumSize(100, event->size().height() / 5);
    // m_uiWavePulse->setMinimumSize(100, event->size().height() / 5);
    // m_uiDataTable->setMinimumSize(100, event->size().height()/5);
}



void UiMonitorSteam::timeToAddData()
{
  

}
