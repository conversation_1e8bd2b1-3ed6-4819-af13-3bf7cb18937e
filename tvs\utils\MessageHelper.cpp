#include "MessageHelper.h"
#include "app/AppConfig.h"

void MessageHelper::information(QWidget *parent, const QString &message)
{
    QMessageBox::information(parent, 
                           APP_CONFIG.windowTitle(), 
                           message);
}

QMessageBox::StandardButton MessageHelper::warning(QWidget *parent, 
                                                 const QString &message,
                                                 QMessageBox::StandardButtons buttons,
                                                 QMessageBox::StandardButton defaultButton)
{
    return QMessageBox::warning(parent,
                              APP_CONFIG.windowTitle(),
                              message,
                              buttons,
                              defaultButton);
}

QMessageBox::StandardButton MessageHelper::question(QWidget *parent, 
                                                  const QString &message,
                                                  QMessageBox::StandardButtons buttons,
                                                  QMessageBox::StandardButton defaultButton)
{
    return QMessageBox::question(parent,
                               APP_CONFIG.windowTitle(),
                               message,
                               buttons,
                               defaultButton);
}

void MessageHelper::critical(QWidget *parent, const QString &message)
{
    QMessageBox::critical(parent,
                         APP_CONFIG.windowTitle(),
                         message);
}

void MessageHelper::about(QWidget *parent, const QString &message)
{
    QMessageBox::about(parent,
                      APP_CONFIG.windowTitle(),
                      message);
}
