/**
 * @file template_usage_example.h
 * @brief SARibbonJson 模板类使用示例
 * 
 * 展示如何使用 SARibbonJson 模板类继承自不同的基类
 */

#ifndef TEMPLATE_USAGE_EXAMPLE_H
#define TEMPLATE_USAGE_EXAMPLE_H

#include "SARibbonMainWindowJson.h"
#include <QMainWindow>

// 示例 1: 使用类型别名（最常用）
class StandardRibbonWindow : public SARibbonMainWindowJson
{
    Q_OBJECT
public:
    StandardRibbonWindow(QWidget *parent = nullptr)
        : SARibbonMainWindowJson(parent)
    {
        initRibbonByJson("./config/ribbon.json");
    }
};

// 示例 2: 直接使用模板类
class DirectTemplateWindow : public SARibbonJson<SARibbonMainWindow>
{
    Q_OBJECT
public:
    DirectTemplateWindow(QWidget *parent = nullptr)
        : SARibbonJson<SARibbonMainWindow>(parent)
    {
        initRibbonByJson("./config/ribbon.json");
    }
};

// 示例 3: 继承自 QMainWindow（如果不需要完整的 SARibbonMainWindow 功能）
class SimpleRibbonWindow : public SARibbonJson<QMainWindow>
{
    Q_OBJECT
private:
    SARibbonBar* m_ribbonBar;

public:
    SimpleRibbonWindow(QWidget *parent = nullptr)
        : SARibbonJson<QMainWindow>(parent)
        , m_ribbonBar(nullptr)
    {
        setupRibbon();
        initRibbonByJson("./config/ribbon.json");
    }

    // 需要提供 ribbonBar() 方法，因为模板类会调用它
    SARibbonBar* ribbonBar() const
    {
        return m_ribbonBar;
    }

private:
    void setupRibbon()
    {
        m_ribbonBar = new SARibbonBar(this);
        // 设置为中央控件或者添加到布局中
        // 这里需要根据具体需求来实现
    }
};

// 示例 4: 自定义基类
class CustomBaseWindow : public QWidget
{
    Q_OBJECT
private:
    SARibbonBar* m_ribbonBar;

public:
    CustomBaseWindow(QWidget *parent = nullptr)
        : QWidget(parent)
        , m_ribbonBar(new SARibbonBar(this))
    {
    }

    SARibbonBar* ribbonBar() const { return m_ribbonBar; }
};

class CustomRibbonWindow : public SARibbonJson<CustomBaseWindow>
{
    Q_OBJECT
public:
    CustomRibbonWindow(QWidget *parent = nullptr)
        : SARibbonJson<CustomBaseWindow>(parent)
    {
        initRibbonByJson("./config/ribbon.json");
    }

protected:
    // 重写方法以添加自定义逻辑
    virtual QAction* createAction(const QString& text, const QString& iconPath) override
    {
        QAction* action = SARibbonJson<CustomBaseWindow>::createAction(text, iconPath);
        action->setToolTip(QString("自定义提示: %1").arg(text));
        return action;
    }

    virtual bool processCustomControlType(const QString& type, const QJsonObject& actionObj, SARibbonPannel* pannel) override
    {
        if (type == "custom_widget") {
            // 处理自定义控件类型
            auto widget = new QLabel(actionObj.value("title").toString());
            pannel->addLargeWidget(widget);
            
            auto cmd = actionObj.value("cmd").toString();
            if (!cmd.isEmpty()) {
                registerControl(cmd, widget);
            }
            
            return true;
        }
        
        return SARibbonJson<CustomBaseWindow>::processCustomControlType(type, actionObj, pannel);
    }
};

// 示例 5: 多重继承（高级用法）
class MultiInheritanceBase
{
public:
    virtual void customMethod() = 0;
};

class MultiRibbonWindow : public SARibbonJson<SARibbonMainWindow>, public MultiInheritanceBase
{
    Q_OBJECT
public:
    MultiRibbonWindow(QWidget *parent = nullptr)
        : SARibbonJson<SARibbonMainWindow>(parent)
    {
        initRibbonByJson("./config/ribbon.json");
    }

    virtual void customMethod() override
    {
        // 实现自定义方法
    }
};

#endif // TEMPLATE_USAGE_EXAMPLE_H
