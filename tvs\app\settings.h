/*
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-05-12 20:37:40
 * @LastEditors: zhai
 * @LastEditTime: 2025-07-12 11:02:34
 */
#ifndef SETTINGS_H
#define SETTINGS_H

/******************************************************************************
  File Name     : settings.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 程序全局设置保存及读取
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/


#include "utils/singleton.h"

#include <QString>
#include <QVariant>
#include "app/YamlConfig.h"

class QSettings;


template<typename T>
QString array2string(const QVector<T> &array)
{
    QStringList strList;
    for (const T &item : array)
    {
        strList << QVariant(item).toString();
    }
    return strList.join(",");
}

template<typename T>
QVector<T> string2array(const QString &str)
{
    QVector<T> array;
    QStringList parts = str.split(",", Qt::SkipEmptyParts);
    for (const QString &part : parts)
    {
        array.append(QVariant(part).value<T>());
    }
    return array;
}

class AppSettings : public Singleton<AppSettings>
{
public:
    void set(QAnyStringView key, const QVariant &value);
    QVariant get(QAnyStringView key, const QVariant &defaultValue) const;
    QVariant get(QAnyStringView key) const;

    template<typename T>
    void setArray(QAnyStringView key, const QVector<T> &array)
    {
        set(key, array2string(array));
    }

    template<typename T>
    QVector<T> getArray(QAnyStringView key)
    {
        return string2array<T>(get(key).toString());
    }


private:
    AppSettings();
    friend class Singleton<AppSettings>;

private:
    QSettings *m_settings;
};

class YamlConfigUI : public YamlConfig, public Singleton<YamlConfigUI>
{
private:
    // 保持原来的构造函数实现，通过基类构造函数传递配置文件路径
    YamlConfigUI() : YamlConfig("./config/ui.yaml")
    {
    }

    friend class Singleton<YamlConfigUI>;
};

class YamlConfigPj : public YamlConfig, public Singleton<YamlConfigPj>
{
private:
    // 保持原来的构造函数实现，通过基类构造函数传递配置文件路径
    YamlConfigPj() : YamlConfig("./config/pj.yaml")
    {
    }

    friend class Singleton<YamlConfigPj>;
};

class YamlConfigNode : public YamlConfig, public Singleton<YamlConfigNode>
{
private:
    // 保持原来的构造函数实现，通过基类构造函数传递配置文件路径
    YamlConfigNode() : YamlConfig("./config/ts1.yaml")
    {
    }

    friend class Singleton<YamlConfigNode>;
};

#endif // SETTINGS_H
