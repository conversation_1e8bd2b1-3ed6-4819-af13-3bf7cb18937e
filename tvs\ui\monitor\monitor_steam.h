#ifndef MONITOR_STEAM_H
#define MONITOR_STEAM_H

#include <QWidget>
#include <QLabel>

namespace YAML {
    class Node;
}

class InfoCard : public QFrame
{
    Q_OBJECT
public:
    explicit InfoCard(const QString &name, const QString &code, QWidget *parent = nullptr);
    void updateValue(double value);
    void setUnit(const QString &unit);

private:
    void setupUI();
    QString formatValue(double value) const;

    QLabel *m_nameLabel;
    QLabel *m_codeLabel;
    QLabel *m_valueLabel;
    QLabel *m_unitLabel;
    QString m_unit;
};


class UiMonitorSteam : public QWidget
{
    Q_OBJECT
public:
    explicit UiMonitorSteam(const QString &id, QWidget *parent = nullptr);

    bool isUiDataVisiable() const;
    void setUiDataVisiable(bool visible);

protected:
    void resizeEvent(QResizeEvent *event) override;

private:
    QWidget* createSectionWidget(const QString& title, const QString& defaultUnit, 
                               const YAML::Node& pointsNode);

private slots:
    void timeToAddData();
};

#endif // MONITOR_STEAM_H
