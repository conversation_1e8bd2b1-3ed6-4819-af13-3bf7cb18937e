int(??)
double(??)
float(??)
char(??)
bool(??)
void(??)
const(??)
static(??)
if(??)
else(??)
for(??)
while(??)
do(??)
switch(??)
case(??)
break(??)
continue(??)
return(??)
true(??)
false(??)
nullptr(??)
auto(??)
using(??)
namespace(??)
class(??)
struct(??)
template(??)
typename(??)
public(??)
private(??)
protected(??)
virtual(??)
override(??)
final(??)
new(??)
delete(??)
sizeof(??)
typedef(??)
typeid(??)
dynamic_cast(??)
static_cast(??)
reinterpret_cast(??)
const_cast(??)
try(??)
catch(??)
throw(??)
noexcept(??)
operator(??)
friend(??)
inline(??)
explicit(??)
mutable(??)
volatile(??)
extern(??)
register(??)
alignas(??)
alignof(??)
and(??)
or(??)
not(??)
xor(??)
bitand(??)
bitor(??)
compl(??)
and_eq(??)
or_eq(??)
xor_eq(??)
not_eq(??)
include(??)
std::vector(??)
std::string(??)
std::cout(??)
std::cin(??)
std::endl(??)
std::map(??)
std::unordered_map(??)
std::set(??)
std::unordered_set(??)
std::pair(??)
std::make_pair(??)
std::tuple(??)
std::make_tuple(??)
std::array(??)
std::list(??)
std::deque(??)
std::stack(??)
std::queue(??)
std::priority_queue(??)
std::unique_ptr(??)
std::shared_ptr(??)
std::weak_ptr(??)
std::move(??)
std::forward(??)
std::begin(??)
std::end(??)
std::size(??)
std::empty(??)
std::swap(??)
std::sort(??)
std::find(??)
std::copy(??)
std::transform(??)
std::accumulate(??)
std::max(??)
std::min(??)
std::abs(??)
std::sqrt(??)
std::pow(??)
std::log(??)
std::exp(??)
std::sin(??)
std::cos(??)
std::tan(??)
std::asin(??)
std::acos(??)
std::atan(??)
std::ceil(??)
std::floor(??)
std::round(??)
std::to_string(??)
std::stoi(??)
std::stod(??)
std::stof(??)
std::stol(??)
std::stoll(??)
std::stoul(??)
std::stoull(??)
std::exception(??)
std::runtime_error(??)
std::logic_error(??)
std::invalid_argument(??)
std::out_of_range(??)
std::bad_alloc(??)
std::nothrow(??)
std::thread(??)
std::mutex(??)
std::lock_guard(??)
std::unique_lock(??)
std::condition_variable(??)
std::atomic(??)
std::future(??)
std::promise(??)
std::async(??)
std::chrono::system_clock(??)
std::chrono::steady_clock(??)
std::chrono::high_resolution_clock(??)
std::chrono::duration(??)
std::chrono::time_point(??)
std::filesystem::path(??)
std::filesystem::exists(??)
std::filesystem::create_directory(??)
std::filesystem::remove(??)
std::filesystem::rename(??)
std::filesystem::file_size(??)
std::regex(??)
std::smatch(??)
std::regex_match(??)
std::regex_search(??)
std::regex_replace(??)
std::istream(??)
std::ostream(??)
std::ifstream(??)
std::ofstream(??)
std::stringstream(??)
std::istringstream(??)
std::ostringstream(??)
std::getline(??)
std::cin.get(??)
std::cin.ignore(??)
std::cin.clear(??)
std::cout.precision(??)
std::cout.width(??)
std::cout.fill(??)
std::cout.flush(??)
std::cerr(??)
std::clog(??)
std::wcout(??)
std::wcin(??)
std::wcerr(??)
std::wclog(??)
std::wstring(??)
std::wstringstream(??)
std::wistringstream(??)
std::wostringstream(??)
std::wregex(??)
std::wsmatch(??)
std::wregex_match(??)
std::wregex_search(??)
std::wregex_replace(??)
std::sort(??)
std::find(??)
std::copy(??)
std::copy_if(??)
std::transform(??)
std::accumulate(??)
std::for_each(??)
std::remove(??)
std::remove_if(??)
std::replace(??)
std::replace_if(??)
std::reverse(??)
std::rotate(??)
std::unique(??)
std::lower_bound(??)
std::upper_bound(??)
std::binary_search(??)
std::merge(??)
std::inplace_merge(??)
std::set_union(??)
std::set_intersection(??)
std::set_difference(??)
std::set_symmetric_difference(??)
std::min_element(??)
std::max_element(??)
std::count(??)
std::count_if(??)
std::all_of(??)
std::any_of(??)
std::none_of(??)
std::generate(??)
std::generate_n(??)
std::iota(??)
std::shuffle(??)
std::sample(??)
std::partition(??)
std::stable_partition(??)
std::is_sorted(??)
std::is_partitioned(??)
std::is_heap(??)
std::make_heap(??)
std::push_heap(??)
std::pop_heap(??)
std::sort_heap(??)
std::next_permutation(??)
std::prev_permutation(??)
std::clamp(??)
std::gcd(??)
std::lcm(??)
std::memcpy(??)
std::memmove(??)
std::memset(??)
std::strlen(??)
std::strcmp(??)
std::strcpy(??)
std::strcat(??)
std::strstr(??)
std::strtok(??)
std::atoi(??)
std::atol(??)
std::atof(??)
std::rand(??)
std::srand(??)
std::exit(??)
std::atexit(??)
std::abort(??)
std::quick_exit(??)
std::at_quick_exit(??)
std::system(??)
std::getenv(??)
std::putenv(??)
std::setenv(??)
std::unsetenv(??)
std::perror(??)
std::signal(??)
std::raise(??)
std::setjmp(??)
std::longjmp(??)
std::va_start(??)
std::va_arg(??)
std::va_end(??)
std::va_copy(??)
std::offsetof(??)
std::align(??)
std::hardware_destructive_interference_size(??)
std::hardware_constructive_interference_size(??)