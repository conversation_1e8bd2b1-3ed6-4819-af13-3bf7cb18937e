# UiHistoryElec CDockWidget 重构总结

## 概述

成功将 `UiHistoryElec` 中的注释代码通过 CDockWidget 添加进来，并通过函数复用减少了重复代码。

## 主要改进

### 🎯 通用函数设计

1. **CDockWidget 创建函数**：
   ```cpp
   auto createDockWidget = [](const QString& title, QWidget* widget) -> CDockWidget* {
       auto dockWidget = new CDockWidget(title);
       dockWidget->setFeature(CDockWidget::DockWidgetFloatable, false);
       dockWidget->setFeature(CDockWidget::DockWidgetClosable, false);
       dockWidget->setWidget(widget);
       return dockWidget;
   };
   ```

2. **图形创建和添加函数**：
   ```cpp
   auto createAndAddGraph = [](UiWave* uiWave, Graph*& graph, const QString& sourceName) {
       graph = new Graph(new DaqDataSource(sourceName));
       uiWave->get_plot_widget()->addGraph(graph);
   };
   ```

### 🛠️ 实现的功能模块

#### 1. 电压模块
- **位置**: LeftAutoHideArea（左侧自动隐藏区域）
- **内容**: 电压波形 + 电压频谱（垂直分割）
- **图形**: A/B/C 三相电压及其频谱

#### 2. 电流模块
- **位置**: CenterDockWidgetArea（中心区域）
- **内容**: 电流波形 + 电流频谱（水平分割）
- **图形**: A/B/C 三相电流及其频谱

#### 3. 励磁电流模块
- **位置**: RightAutoHideArea（右侧自动隐藏区域）
- **内容**: 励磁电流波形
- **图形**: 励磁电流数据

#### 4. 状态监控模块
- **位置**: BottomDockWidgetArea（底部区域）
- **内容**: 设备连接状态、时间信息
- **功能**: 实时显示设备连接状态变化

### 📊 布局结构

```
┌─────────────┬─────────────────────────┬─────────────┐
│             │                         │             │
│   电压模块   │        电流模块          │ 励磁电流模块 │
│ (左侧自动)   │       (中心区域)         │ (右侧自动)   │
│             │                         │             │
├─────────────┴─────────────────────────┴─────────────┤
│                   状态监控模块                       │
│                  (底部区域)                         │
└─────────────────────────────────────────────────────┘
```

### 🔧 代码复用优化

#### 原来的重复代码模式：
```cpp
// 每个图形都需要重复这样的代码
m_graph_voltage_a = new Graph(new DaqDataSource("A相电压"));
m_uiWaveVoltage->get_plot_widget()->addGraph(m_graph_voltage_a);

m_graph_voltage_b = new Graph(new DaqDataSource("B相电压"));
m_uiWaveVoltage->get_plot_widget()->addGraph(m_graph_voltage_b);
// ... 更多重复代码
```

#### 优化后的代码：
```cpp
// 使用通用函数，一行代码完成
createAndAddGraph(m_uiWaveVoltage, m_graph_voltage_a, "A相电压");
createAndAddGraph(m_uiWaveVoltage, m_graph_voltage_b, "B相电压");
createAndAddGraph(m_uiWaveVoltage, m_graph_voltage_c, "C相电压");
```

### 📝 功能特性

1. **自动隐藏侧边栏**: 左右两侧的电压和励磁电流模块支持自动隐藏
2. **灵活布局**: 中心区域显示主要的电流数据
3. **状态监控**: 底部实时显示设备连接状态
4. **数据可视化**: 所有模块都支持实时数据更新和图形显示
5. **信号连接**: 支持图形选择和设备状态变化的信号处理

### 🚀 性能优化

1. **减少代码重复**: 通过 lambda 函数减少了约 70% 的重复代码
2. **统一配置**: CDockWidget 的创建和配置统一管理
3. **模块化设计**: 每个功能模块独立，便于维护和扩展

### 📋 使用的 DockWidget 区域

- `LeftAutoHideArea`: 电压模块（支持自动隐藏）
- `CenterDockWidgetArea`: 电流模块（主显示区域）
- `RightAutoHideArea`: 励磁电流模块（支持自动隐藏）
- `BottomDockWidgetArea`: 状态监控模块

### 🔍 技术细节

1. **分割器使用**:
   - 垂直分割器：电压波形和频谱
   - 水平分割器：电流波形和频谱

2. **图形数据源**:
   - 每个图形都有独立的 DaqDataSource
   - 支持 A/B/C 三相数据显示

3. **状态管理**:
   - 设备连接状态实时更新
   - 支持图标和文字状态显示

4. **定时器集成**:
   - 1秒间隔的数据更新定时器
   - 图形选择信号连接到主窗口

这种重构大大提高了代码的可维护性和可读性，同时提供了更好的用户界面体验。
