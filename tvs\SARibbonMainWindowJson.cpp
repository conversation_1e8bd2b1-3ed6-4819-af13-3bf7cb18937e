#include "SARibbonMainWindowJson.h"
#include "SARibbonBar.h"
#include "SARibbonCategory.h"
#include "SARibbonPannel.h"
#include "SARibbonToolButton.h"
#include "SARibbonContextCategory.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QDebug>
#include <QAction>
#include <QIcon>
#include <QWidget>
#include <QLabel>
#include <QDateTimeEdit>
#include <QHBoxLayout>
#include "utils/actionmanager.h"

SARibbonMainWindowJson::SARibbonMainWindowJson(QWidget *parent, bool useRibbon, const Qt::WindowFlags flags)
    : SARibbonMainWindow(parent, useRibbon, flags)
{
}

SARibbonMainWindowJson::~SARibbonMainWindowJson()
{
}

bool SARibbonMainWindowJson::initRibbonByJson(const QString& jsonFilePath)
{
    QFile jsfile(jsonFilePath);
    if (!jsfile.open(QIODevice::ReadOnly)) {
        qDebug() << "无法打开 JSON 文件：" << jsonFilePath;
        return false;
    }

    QJsonParseError err;
    QJsonDocument jsdoc = QJsonDocument::fromJson(jsfile.readAll(), &err);

    if (err.error != QJsonParseError::NoError) {
        qDebug() << "JSON 格式错误：" << err.errorString();
        return false;
    }

    SARibbonBar* ribbon = ribbonBar();
    if (!ribbon) {
        qDebug() << "Ribbon Bar 未初始化";
        return false;
    }

    QJsonObject rootObj = jsdoc.object();
    auto jribbon = rootObj.value("ribbon").toObject();

    // 处理应用按钮
    auto japp_btn = jribbon.value("app_button").toObject();
    auto app_btn_show = japp_btn.value("show").toBool(true);
    ribbon->applicationButton()->setVisible(app_btn_show);

    // 处理分类
    auto jcategorys = jribbon.value("categorys").toArray();
    for (const QJsonValue &jcat : jcategorys) {
        auto jcategory = jcat.toObject();
        processJsonCategory(jcategory, ribbon);
    }

    // 处理上下文分类
    auto jcontext_category = jribbon.value("context_category").toArray();
    for (const QJsonValue &jcat : jcontext_category) {
        auto jcategory = jcat.toObject();
        processJsonContextCategory(jcategory, ribbon);
    }

    return true;
}

bool SARibbonMainWindowJson::hasControl(const QString& cmd) const
{
    return m_cmd_control_map.contains(cmd);
}

SARibbonToolButton* SARibbonMainWindowJson::getRibbonToolButton(const QString& cmd) const
{
    return getControl<SARibbonToolButton>(cmd);
}

void SARibbonMainWindowJson::enableControlByCmd(const QString& cmd, bool enabled)
{
    auto toolButton = getRibbonToolButton(cmd);
    if (toolButton) {
        toolButton->setEnabled(enabled);
        qDebug() << "设置控件" << cmd << "启用状态为：" << enabled;
    } else {
        qDebug() << "未找到cmd为" << cmd << "的控件";
    }
}

void SARibbonMainWindowJson::setControlVisibleByCmd(const QString& cmd, bool visible)
{
    auto toolButton = getRibbonToolButton(cmd);
    if (toolButton) {
        toolButton->setVisible(visible);
        qDebug() << "设置控件" << cmd << "可见性为：" << visible;
    } else {
        qDebug() << "未找到cmd为" << cmd << "的控件";
    }
}

QAction* SARibbonMainWindowJson::createAction(const QString& text, const QString& iconPath)
{
    QAction* action = new QAction(text, this);
    if (!iconPath.isEmpty()) {
        action->setIcon(QIcon(iconPath));
    }
    return action;
}

void SARibbonMainWindowJson::processJsonCategory(const QJsonObject& categoryObj, SARibbonBar* ribbon)
{
    auto category_title = categoryObj.value("title").toString();
    auto category_visible = categoryObj.value("visible").toBool(true);

    SARibbonCategory* category = new SARibbonCategory();
    category->setCategoryName(category_title);
    ribbon->addCategoryPage(category);
    category->setVisible(category_visible);

    // 处理面板
    auto jpannels = categoryObj.value("pannels").toArray();
    for (const QJsonValue &jpan : jpannels) {
        auto jpannel = jpan.toObject();
        processJsonPannel(jpannel, category);
    }
}

void SARibbonMainWindowJson::processJsonPannel(const QJsonObject& pannelObj, SARibbonCategory* category)
{
    auto pannel_title = pannelObj.value("title").toString();
    SARibbonPannel* pannel = category->addPannel(pannel_title);

    // 处理动作
    auto jactions = pannelObj.value("actions").toArray();
    for (const QJsonValue &jact : jactions) {
        auto jaction = jact.toObject();
        processJsonAction(jaction, pannel);
    }
}

void SARibbonMainWindowJson::processJsonAction(const QJsonObject& actionObj, SARibbonPannel* pannel)
{
    auto action_title = actionObj.value("title").toString();
    auto action_icon = actionObj.value("icon").toString();
    auto action_cmd = actionObj.value("cmd").toString();
    auto checkable = actionObj.value("checkable").toBool();
    auto disable = actionObj.value("disable").toBool();
    auto visible = actionObj.value("visible").toBool(true);
    auto type = actionObj.contains("type") ? actionObj.value("type").toString() : "action";

    // 尝试处理自定义控件类型
    if (type != "action" && processCustomControlType(type, actionObj, pannel)) {
        return;
    }

    // 处理标准 action
    if (type == "action") {
        QAction *action = createAction(action_title, action_icon);
        action->setCheckable(checkable);
        action->setVisible(visible);
        action->setDisabled(disable);
        auto toolButton = pannel->addLargeAction(action);

        // 注册控件到映射中
        if (!action_cmd.isEmpty() && toolButton) {
            registerControl(action_cmd, toolButton);
        }

        // 连接 action 信号到 ActionManager
        if (!action_cmd.isEmpty()) {
            connect(action, &QAction::triggered, [action_cmd](bool checked) {
                qDebug() << "on ribbon cmd: " << action_cmd;
                ActionManager::instance().executeCommand(action_cmd, checked);
            });
        }
    }
}

bool SARibbonMainWindowJson::processCustomControlType(const QString& type, const QJsonObject& actionObj, SARibbonPannel* pannel)
{
    if (type == "datetime") {
        // 处理日期时间控件
        auto w = new QWidget();
        w->setFixedHeight(26);
        w->setContentsMargins(18, 0, 18, 0);

        auto layout = new QHBoxLayout(w);
        layout->setContentsMargins(0, 0, 0, 0);
        layout->setSpacing(6);

        auto label = new QLabel(actionObj.value("title").toString());
        auto dateTimeEdit = new QDateTimeEdit();
        dateTimeEdit->setDisplayFormat("yyyy-MM-dd hh:mm:ss");

        layout->addWidget(label);
        layout->addWidget(dateTimeEdit);

        pannel->addLargeWidget(w);

        // 注册控件到映射中
        auto action_cmd = actionObj.value("cmd").toString();
        if (!action_cmd.isEmpty()) {
            registerControl(action_cmd, dateTimeEdit);
        }

        return true;
    }

    return false; // 未处理的类型
}

void SARibbonMainWindowJson::processJsonContextCategory(const QJsonObject& contextCategoryObj, SARibbonBar* ribbon)
{
    auto category_title = contextCategoryObj.value("title").toString();
    auto category_id = contextCategoryObj.value("id").toInt();
    auto category_color = contextCategoryObj.value("color").toString();

    auto context_category = ribbon->addContextCategory(category_title, QColor(category_color), category_id);

    auto jpages = contextCategoryObj.value("pages").toArray();
    for (const QJsonValue &jp : jpages) {
        auto jpage = jp.toObject();
        auto page_title = jpage.value("title").toString();
        SARibbonCategory *page = context_category->addCategoryPage(page_title);

        auto jpannels = jpage.value("pannels").toArray();
        for (const QJsonValue &jpan : jpannels) {
            auto jpannel = jpan.toObject();
            processJsonPannel(jpannel, page);
        }
    }

    ribbon->showContextCategory(context_category);
}
