#ifndef SARIBBONMAINWINDOWJSON_H
#define SARIBBONMAINWINDOWJSON_H

#include <QMap>
#include <QVariant>
#include <utility>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QDebug>
#include <QAction>
#include <QIcon>
#include <QWidget>
#include <QLabel>
#include <QDateTimeEdit>
#include <QHBoxLayout>
#include "SARibbonMainWindow.h"
#include "SARibbonBar.h"
#include "SARibbonCategory.h"
#include "SARibbonPannel.h"
#include "SARibbonToolButton.h"
#include "SARibbonContextCategory.h"
#include "utils/actionmanager.h"

class SARibbonToolButton;
class SARibbonBar;
class SARibbonCategory;
class SARibbonPannel;

/**
 * @brief 支持 JSON 配置的 Ribbon 模板基类
 *
 * 这个模板类可以继承自任何基类，提供了以下功能：
 * 1. 从 JSON 文件加载 Ribbon 配置
 * 2. 泛型控件映射系统，支持根据 cmd 查询控件
 * 3. 便捷的控件操作方法
 *
 * @tparam T 基类类型，通常是 SARibbonMainWindow 或其子类
 */
template<typename T>
class SARibbonJson : public T
{
    Q_OBJECT

public:
    template<typename... Args>
    explicit SARibbonJson(Args&&... args) : T(std::forward<Args>(args)...) {}
    virtual ~SARibbonJson() = default;

    // JSON 配置相关
    /**
     * @brief 从 JSON 文件初始化 Ribbon
     * @param jsonFilePath JSON 配置文件路径，默认为 "./config/ribbon.json"
     * @return 是否成功加载
     */
    bool initRibbonByJson(const QString& jsonFilePath = "./config/ribbon.json");

    // 泛型控件映射功能
    /**
     * @brief 注册控件到映射中
     * @param cmd 命令字符串
     * @param control 控件指针
     */
    template<typename ControlType>
    void registerControl(const QString& cmd, ControlType* control)
    {
        if (control != nullptr && !cmd.isEmpty()) {
            m_cmd_control_map[cmd] = QVariant::fromValue(static_cast<void*>(control));
        }
    }

    /**
     * @brief 根据 cmd 获取控件
     * @param cmd 命令字符串
     * @return 控件指针，如果未找到返回 nullptr
     */
    template<typename ControlType>
    ControlType* getControl(const QString& cmd) const
    {
        auto it = m_cmd_control_map.find(cmd);
        if (it != m_cmd_control_map.end()) {
            void* ptr = it.value().value<void*>();
            return static_cast<ControlType*>(ptr);
        }
        return nullptr;
    }
    
    /**
     * @brief 检查 cmd 是否存在对应的控件
     * @param cmd 命令字符串
     * @return 是否存在
     */
    bool hasControl(const QString& cmd) const;

    // 便捷方法
    /**
     * @brief 获取 RibbonToolButton 控件
     * @param cmd 命令字符串
     * @return SARibbonToolButton 指针，如果未找到返回 nullptr
     */
    SARibbonToolButton* getRibbonToolButton(const QString& cmd) const;
    
    /**
     * @brief 根据 cmd 启用/禁用控件
     * @param cmd 命令字符串
     * @param enabled 是否启用
     */
    void enableControlByCmd(const QString& cmd, bool enabled = true);
    
    /**
     * @brief 根据 cmd 设置控件可见性
     * @param cmd 命令字符串
     * @param visible 是否可见
     */
    void setControlVisibleByCmd(const QString& cmd, bool visible = true);

protected:
    /**
     * @brief 创建 QAction 的虚函数，子类可以重写以自定义 Action 创建
     * @param text 文本
     * @param iconPath 图标路径
     * @return QAction 指针
     */
    virtual QAction* createAction(const QString& text, const QString& iconPath);

    /**
     * @brief 处理 JSON 中的 action 配置
     * @param actionObj JSON action 对象
     * @param pannel 目标面板
     */
    virtual void processJsonAction(const QJsonObject& actionObj, SARibbonPannel* pannel);

    /**
     * @brief 处理 JSON 中的自定义控件类型
     * @param type 控件类型
     * @param actionObj JSON action 对象
     * @param pannel 目标面板
     * @return 是否处理成功
     */
    virtual bool processCustomControlType(const QString& type, const QJsonObject& actionObj, SARibbonPannel* pannel);

private:
    // cmd 到控件的映射
    QMap<QString, QVariant> m_cmd_control_map;
    
    // 内部辅助方法
    void processJsonCategory(const QJsonObject& categoryObj, SARibbonBar* ribbon);
    void processJsonContextCategory(const QJsonObject& contextCategoryObj, SARibbonBar* ribbon);
    void processJsonPannel(const QJsonObject& pannelObj, SARibbonCategory* category);
};

// ========== 模板类方法实现 ==========

template<typename T>
bool SARibbonJson<T>::initRibbonByJson(const QString& jsonFilePath)
{
    QFile jsfile(jsonFilePath);
    if (!jsfile.open(QIODevice::ReadOnly)) {
        qDebug() << "无法打开 JSON 文件：" << jsonFilePath;
        return false;
    }

    QJsonParseError err;
    QJsonDocument jsdoc = QJsonDocument::fromJson(jsfile.readAll(), &err);

    if (err.error != QJsonParseError::NoError) {
        qDebug() << "JSON 格式错误：" << err.errorString();
        return false;
    }

    SARibbonBar* ribbon = this->ribbonBar();
    if (!ribbon) {
        qDebug() << "Ribbon Bar 未初始化";
        return false;
    }

    QJsonObject rootObj = jsdoc.object();
    auto jribbon = rootObj.value("ribbon").toObject();

    // 处理应用按钮
    auto japp_btn = jribbon.value("app_button").toObject();
    auto app_btn_show = japp_btn.value("show").toBool(true);
    ribbon->applicationButton()->setVisible(app_btn_show);

    // 处理分类
    auto jcategorys = jribbon.value("categorys").toArray();
    for (const QJsonValue &jcat : jcategorys) {
        auto jcategory = jcat.toObject();
        processJsonCategory(jcategory, ribbon);
    }

    // 处理上下文分类
    auto jcontext_category = jribbon.value("context_category").toArray();
    for (const QJsonValue &jcat : jcontext_category) {
        auto jcategory = jcat.toObject();
        processJsonContextCategory(jcategory, ribbon);
    }

    return true;
}

template<typename T>
bool SARibbonJson<T>::hasControl(const QString& cmd) const
{
    return m_cmd_control_map.contains(cmd);
}

template<typename T>
SARibbonToolButton* SARibbonJson<T>::getRibbonToolButton(const QString& cmd) const
{
    return getControl<SARibbonToolButton>(cmd);
}

template<typename T>
void SARibbonJson<T>::enableControlByCmd(const QString& cmd, bool enabled)
{
    auto toolButton = getRibbonToolButton(cmd);
    if (toolButton) {
        toolButton->setEnabled(enabled);
        qDebug() << "设置控件" << cmd << "启用状态为：" << enabled;
    } else {
        qDebug() << "未找到cmd为" << cmd << "的控件";
    }
}

template<typename T>
void SARibbonJson<T>::setControlVisibleByCmd(const QString& cmd, bool visible)
{
    auto toolButton = getRibbonToolButton(cmd);
    if (toolButton) {
        toolButton->setVisible(visible);
        qDebug() << "设置控件" << cmd << "可见性为：" << visible;
    } else {
        qDebug() << "未找到cmd为" << cmd << "的控件";
    }
}

template<typename T>
QAction* SARibbonJson<T>::createAction(const QString& text, const QString& iconPath)
{
    QAction* action = new QAction(text, this);
    if (!iconPath.isEmpty()) {
        action->setIcon(QIcon(iconPath));
    }
    return action;
}

template<typename T>
void SARibbonJson<T>::processJsonCategory(const QJsonObject& categoryObj, SARibbonBar* ribbon)
{
    auto category_title = categoryObj.value("title").toString();
    auto category_visible = categoryObj.value("visible").toBool(true);

    SARibbonCategory* category = new SARibbonCategory();
    category->setCategoryName(category_title);
    ribbon->addCategoryPage(category);
    category->setVisible(category_visible);

    // 处理面板
    auto jpannels = categoryObj.value("pannels").toArray();
    for (const QJsonValue &jpan : jpannels) {
        auto jpannel = jpan.toObject();
        processJsonPannel(jpannel, category);
    }
}

template<typename T>
void SARibbonJson<T>::processJsonContextCategory(const QJsonObject& contextCategoryObj, SARibbonBar* ribbon)
{
    auto category_title = contextCategoryObj.value("title").toString();
    auto category_id = contextCategoryObj.value("id").toInt();
    auto category_color = contextCategoryObj.value("color").toString();

    auto context_category = ribbon->addContextCategory(category_title, QColor(category_color), category_id);

    auto jpages = contextCategoryObj.value("pages").toArray();
    for (const QJsonValue &jp : jpages) {
        auto jpage = jp.toObject();
        auto page_title = jpage.value("title").toString();
        SARibbonCategory *page = context_category->addCategoryPage(page_title);

        auto jpannels = jpage.value("pannels").toArray();
        for (const QJsonValue &jpan : jpannels) {
            auto jpannel = jpan.toObject();
            processJsonPannel(jpannel, page);
        }
    }

    ribbon->showContextCategory(context_category);
}

template<typename T>
void SARibbonJson<T>::processJsonPannel(const QJsonObject& pannelObj, SARibbonCategory* category)
{
    auto pannel_title = pannelObj.value("title").toString();
    SARibbonPannel* pannel = category->addPannel(pannel_title);

    // 处理动作
    auto jactions = pannelObj.value("actions").toArray();
    for (const QJsonValue &jact : jactions) {
        auto jaction = jact.toObject();
        processJsonAction(jaction, pannel);
    }
}

template<typename T>
void SARibbonJson<T>::processJsonAction(const QJsonObject& actionObj, SARibbonPannel* pannel)
{
    auto action_title = actionObj.value("title").toString();
    auto action_icon = actionObj.value("icon").toString();
    auto action_cmd = actionObj.value("cmd").toString();
    auto checkable = actionObj.value("checkable").toBool();
    auto disable = actionObj.value("disable").toBool();
    auto visible = actionObj.value("visible").toBool(true);
    auto type = actionObj.contains("type") ? actionObj.value("type").toString() : "action";

    // 尝试处理自定义控件类型
    if (type != "action" && processCustomControlType(type, actionObj, pannel)) {
        return;
    }

    // 处理标准 action
    if (type == "action") {
        QAction *action = createAction(action_title, action_icon);
        action->setCheckable(checkable);
        action->setVisible(visible);
        action->setDisabled(disable);
        pannel->addLargeAction(action);

        // 获取对应的 ToolButton 并注册控件到映射中
        if (!action_cmd.isEmpty()) {
            auto toolButton = pannel->actionToRibbonToolButton(action);
            if (toolButton) {
                registerControl(action_cmd, toolButton);
            }
        }

        // 连接 action 信号到 ActionManager
        if (!action_cmd.isEmpty()) {
            QObject::connect(action, &QAction::triggered, [action_cmd](bool checked) {
                qDebug() << "on ribbon cmd: " << action_cmd;
                ActionManager::instance().executeCommand(action_cmd, checked);
            });
        }
    }
}

template<typename T>
bool SARibbonJson<T>::processCustomControlType(const QString& type, const QJsonObject& actionObj, SARibbonPannel* pannel)
{
    if (type == "datetime") {
        // 处理日期时间控件
        auto w = new QWidget();
        w->setFixedHeight(26);
        w->setContentsMargins(18, 0, 18, 0);

        auto layout = new QHBoxLayout(w);
        layout->setContentsMargins(0, 0, 0, 0);
        layout->setSpacing(6);

        auto label = new QLabel(actionObj.value("title").toString());
        auto dateTimeEdit = new QDateTimeEdit();
        dateTimeEdit->setDisplayFormat("yyyy-MM-dd hh:mm:ss");

        layout->addWidget(label);
        layout->addWidget(dateTimeEdit);

        pannel->addLargeWidget(w);

        // 注册控件到映射中
        auto action_cmd = actionObj.value("cmd").toString();
        if (!action_cmd.isEmpty()) {
            registerControl(action_cmd, dateTimeEdit);
        }

        return true;
    }

    return false; // 未处理的类型
}

// 为了方便使用，提供一个类型别名
using SARibbonMainWindowJson = SARibbonJson<SARibbonMainWindow>;

#endif // SARIBBONMAINWINDOWJSON_H
