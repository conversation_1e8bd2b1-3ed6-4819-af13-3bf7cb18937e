#ifndef SARIBBONMAINWINDOWJSON_H
#define SARIBBONMAINWINDOWJSON_H

#include <QMap>
#include <QVariant>
#include "SARibbonMainWindow.h"

class SARibbonToolButton;
class SARibbonBar;
class SARibbonCategory;
class SARibbonPannel;

/**
 * @brief 支持 JSON 配置的 Ribbon 主窗口基类
 * 
 * 这个类继承自 SARibbonMainWindow，提供了以下功能：
 * 1. 从 JSON 文件加载 Ribbon 配置
 * 2. 泛型控件映射系统，支持根据 cmd 查询控件
 * 3. 便捷的控件操作方法
 */
class SARibbonMainWindowJson : public SARibbonMainWindow
{
    Q_OBJECT

public:
    explicit SARibbonMainWindowJson(QWidget *parent = nullptr, bool useRibbon = true, const Qt::WindowFlags flags = {});
    virtual ~SARibbonMainWindowJson();

    // JSON 配置相关
    /**
     * @brief 从 JSON 文件初始化 Ribbon
     * @param jsonFilePath JSON 配置文件路径，默认为 "./config/ribbon.json"
     * @return 是否成功加载
     */
    bool initRibbonByJson(const QString& jsonFilePath = "./config/ribbon.json");

    // 泛型控件映射功能
    /**
     * @brief 注册控件到映射中
     * @param cmd 命令字符串
     * @param control 控件指针
     */
    template<typename T>
    void registerControl(const QString& cmd, T* control);
    
    /**
     * @brief 根据 cmd 获取控件
     * @param cmd 命令字符串
     * @return 控件指针，如果未找到返回 nullptr
     */
    template<typename T>
    T* getControl(const QString& cmd) const;
    
    /**
     * @brief 检查 cmd 是否存在对应的控件
     * @param cmd 命令字符串
     * @return 是否存在
     */
    bool hasControl(const QString& cmd) const;

    // 便捷方法
    /**
     * @brief 获取 RibbonToolButton 控件
     * @param cmd 命令字符串
     * @return SARibbonToolButton 指针，如果未找到返回 nullptr
     */
    SARibbonToolButton* getRibbonToolButton(const QString& cmd) const;
    
    /**
     * @brief 根据 cmd 启用/禁用控件
     * @param cmd 命令字符串
     * @param enabled 是否启用
     */
    void enableControlByCmd(const QString& cmd, bool enabled = true);
    
    /**
     * @brief 根据 cmd 设置控件可见性
     * @param cmd 命令字符串
     * @param visible 是否可见
     */
    void setControlVisibleByCmd(const QString& cmd, bool visible = true);

protected:
    /**
     * @brief 创建 QAction 的虚函数，子类可以重写以自定义 Action 创建
     * @param text 文本
     * @param iconPath 图标路径
     * @return QAction 指针
     */
    virtual QAction* createAction(const QString& text, const QString& iconPath);

    /**
     * @brief 处理 JSON 中的 action 配置
     * @param actionObj JSON action 对象
     * @param pannel 目标面板
     */
    virtual void processJsonAction(const QJsonObject& actionObj, SARibbonPannel* pannel);

    /**
     * @brief 处理 JSON 中的自定义控件类型
     * @param type 控件类型
     * @param actionObj JSON action 对象
     * @param pannel 目标面板
     * @return 是否处理成功
     */
    virtual bool processCustomControlType(const QString& type, const QJsonObject& actionObj, SARibbonPannel* pannel);

private:
    // cmd 到控件的映射
    QMap<QString, QVariant> m_cmd_control_map;
    
    // 内部辅助方法
    void processJsonCategory(const QJsonObject& categoryObj, SARibbonBar* ribbon);
    void processJsonContextCategory(const QJsonObject& contextCategoryObj, SARibbonBar* ribbon);
    void processJsonPannel(const QJsonObject& pannelObj, SARibbonCategory* category);
};

// 模板函数实现
template<typename T>
void SARibbonMainWindowJson::registerControl(const QString& cmd, T* control)
{
    if (control != nullptr && !cmd.isEmpty()) {
        m_cmd_control_map[cmd] = QVariant::fromValue(static_cast<void*>(control));
    }
}

template<typename T>
T* SARibbonMainWindowJson::getControl(const QString& cmd) const
{
    auto it = m_cmd_control_map.find(cmd);
    if (it != m_cmd_control_map.end()) {
        void* ptr = it.value().value<void*>();
        return static_cast<T*>(ptr);
    }
    return nullptr;
}

#endif // SARIBBONMAINWINDOWJSON_H
