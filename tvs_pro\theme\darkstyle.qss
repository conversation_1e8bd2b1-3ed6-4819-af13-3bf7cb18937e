/* ---------------------------------------------------------------------------

    WARNING! File created programmatically. All changes made in this file will be lost!

    Created by the qtsass compiler v0.4.0

    The definitions are in the "qdarkstyle.qss._styles.scss" module

--------------------------------------------------------------------------- */
/* Light Style - QDarkStyleSheet ------------------------------------------ */
/*

See Qt documentation:

  - https://doc.qt.io/qt-5/stylesheet.html
  - https://doc.qt.io/qt-5/stylesheet-reference.html
  - https://doc.qt.io/qt-5/stylesheet-examples.html

--------------------------------------------------------------------------- */
/* * extra */
$size36= 36px
$size30= 30px
$size24= 24px
$size18= 18px
$size16= 16px
$size12= 12px
/* 
Reset elements -----------------------------------------------------------
Resetting everything helps to unify styles across different operating systems
--------------------------------------------------------------------------- */
*{
  color: #fff;
  font-family: Microsoft YaHei;
  font-size: 12px;
  line-height: 13px;
  selection-background-color: #404040;
  selection-color: #F4F4F4;
}


*:focus {
  outline: none;
}
/* image&icon*/
QMainWindow::separator:horizontal {
  width: 5px;
  margin-top: 2px;
  margin-bottom: 2px;
  image: url(":/qss_icons/dark/rc/toolbar_separator_vertical.png");
}

QMainWindow::separator:vertical {
  height: 5px;
  margin-left: 2px;
  margin-right: 2px;
  image: url(":/qss_icons/dark/rc/toolbar_separator_horizontal.png");
}
QMenu::indicator:non-exclusive:unchecked {
  image: url(":/qss_icons/dark/rc/checkbox_unchecked.png");
}

QMenu::indicator:non-exclusive:unchecked:hover, QMenu::indicator:non-exclusive:unchecked:focus, QMenu::indicator:non-exclusive:unchecked:pressed {
  border: none;
  image: url(":/qss_icons/dark/rc/checkbox_unchecked_focus.png");
}

QMenu::indicator:non-exclusive:unchecked:disabled {
  image: url(":/qss_icons/dark/rc/checkbox_unchecked_disabled.png");
}

QMenu::indicator:non-exclusive:checked {
  image: url(":/qss_icons/dark/rc/checkbox_checked.png");
}

QMenu::indicator:non-exclusive:checked:hover, QMenu::indicator:non-exclusive:checked:focus, QMenu::indicator:non-exclusive:checked:pressed {
  border: none;
  image: url(":/qss_icons/dark/rc/checkbox_checked_focus.png");
}

QMenu::indicator:non-exclusive:checked:disabled {
  image: url(":/qss_icons/dark/rc/checkbox_checked_disabled.png");
}

QMenu::indicator:non-exclusive:indeterminate {
  image: url(":/qss_icons/dark/rc/checkbox_indeterminate.png");
}

QMenu::indicator:non-exclusive:indeterminate:disabled {
  image: url(":/qss_icons/dark/rc/checkbox_indeterminate_disabled.png");
}

QMenu::indicator:non-exclusive:indeterminate:focus, QMenu::indicator:non-exclusive:indeterminate:hover, QMenu::indicator:non-exclusive:indeterminate:pressed {
  image: url(":/qss_icons/dark/rc/checkbox_indeterminate_focus.png");
}

QMenu::indicator:exclusive:unchecked {
  image: url(":/qss_icons/dark/rc/radio_unchecked.png");
}

QMenu::indicator:exclusive:unchecked:hover, QMenu::indicator:exclusive:unchecked:focus, QMenu::indicator:exclusive:unchecked:pressed {
  border: none;
  outline: none;
  image: url(":/qss_icons/dark/rc/radio_unchecked_focus.png");
}
QMenu::indicator:exclusive:unchecked:disabled {
  image: url(":/qss_icons/dark/rc/radio_unchecked_disabled.png");
}

QMenu::indicator:exclusive:checked {
  border: none;
  outline: none;
  image: url(":/qss_icons/dark/rc/radio_checked.png");
}

QMenu::indicator:exclusive:checked:hover, QMenu::indicator:exclusive:checked:focus, QMenu::indicator:exclusive:checked:pressed {
  border: none;
  outline: none;
  image: url(":/qss_icons/dark/rc/radio_checked_focus.png");
}

QMenu::indicator:exclusive:checked:disabled {
  outline: none;
  image: url(":/qss_icons/dark/rc/radio_checked_disabled.png");
}

QMenu::right-arrow {
  margin: 5px;
  padding-left: 12px;
  image: url(":/qss_icons/dark/rc/arrow_right.png");
  height: 12px;
  width: 12px;
}
QSizeGrip {
  background: transparent;
  width: 12px;
  height: 12px;
  image: url(":/qss_icons/dark/rc/window_grip.png");
}
QToolBar::handle:horizontal {
  width: 16px;
  image: url(":/qss_icons/dark/rc/toolbar_move_horizontal.png");
}

QToolBar::handle:vertical {
  height: 16px;
  image: url(":/qss_icons/dark/rc/toolbar_move_vertical.png");
}

QToolBar::separator:horizontal {
  width: 16px;
  image: url(":/qss_icons/dark/rc/toolbar_separator_horizontal.png");
}

QToolBar::separator:vertical {
  height: 16px;
  image: url(":/qss_icons/dark/rc/toolbar_separator_vertical.png");
}


QAbstractSpinBox::up-arrow, QAbstractSpinBox::up-arrow:disabled, QAbstractSpinBox::up-arrow:off {
  image: url(":/qss_icons/dark/rc/arrow_up_disabled.png");
  height: 8px;
  width: 8px;
}

QAbstractSpinBox::up-arrow:hover {
  image: url(":/qss_icons/dark/rc/arrow_up.png");
}
QAbstractSpinBox::down-arrow, QAbstractSpinBox::down-arrow:disabled, QAbstractSpinBox::down-arrow:off {
  image: url(":/qss_icons/dark/rc/arrow_down_disabled.png");
  height: 8px;
  width: 8px;
}

QAbstractSpinBox::down-arrow:hover {
  image: url(":/qss_icons/dark/rc/arrow_down.png");
}
QComboBox::down-arrow {
  image: url(":/qss_icons/dark/rc/arrow_down_disabled.png");
  height: 8px;
  width: 8px;
}

QComboBox::down-arrow:on,
QComboBox::down-arrow:hover, 
QComboBox::down-arrow:focus {
  image: url(":/qss_icons/dark/rc/arrow_down.png");
}
QTabBar::close-button, QDockWidget QTabBar::close-button {
  border: 0;
  margin: 0;
  padding: 4px;
  image: url(":/qss_icons/dark/rc/window_close.png");
}

QTabBar::close-button:hover,
QDockWidget QTabBar::close-button:hover {
  image: url(":/qss_icons/dark/rc/window_close_focus.png");
}

QTabBar::close-button:pressed,
QDockWidget QTabBar::close-button:pressed {
  image: url(":/qss_icons/dark/rc/window_close_pressed.png");
}
QDockWidget {
  outline: 1px solid #455364;
  background-color: #19232D;
  border: 1px solid #455364;
  border-radius: 4px;
  titlebar-close-icon: url(":/qss_icons/dark/rc/transparent.png");
  titlebar-normal-icon: url(":/qss_icons/dark/rc/transparent.png");
}
QDockWidget::close-button {
  icon-size: 12px;
  border: none;
  background: transparent;
  background-image: transparent;
  border: 0;
  margin: 0;
  padding: 0;
  image: url(":/qss_icons/dark/rc/window_close.png");
}

QDockWidget::close-button:hover {
  image: url(":/qss_icons/dark/rc/window_close_focus.png");
}

QDockWidget::close-button:pressed {
  image: url(":/qss_icons/dark/rc/window_close_pressed.png");
}

QDockWidget::float-button {
  icon-size: 12px;
  border: none;
  background: transparent;
  background-image: transparent;
  border: 0;
  margin: 0;
  padding: 0;
  image: url(":/qss_icons/dark/rc/window_undock.png");
}

QDockWidget::float-button:hover {
  image: url(":/qss_icons/dark/rc/window_undock_focus.png");
}

QDockWidget::float-button:pressed {
  image: url(":/qss_icons/dark/rc/window_undock_pressed.png");
}

QTreeView:branch:has-children:!has-siblings:closed, 
QTreeView:branch:closed:has-children:has-siblings {
  border-image: none;
  image: url(":/qss_icons/dark/rc/branch_closed.png");
}

QTreeView:branch:open:has-children:!has-siblings, 
QTreeView:branch:open:has-children:has-siblings {
  border-image: none;
  image: url(":/qss_icons/dark/rc/branch_open.png");
}
QListView::indicator:checked,
QTableView::indicator:checked,
QColumnView::indicator:checked {
  image: url(":/qss_icons/dark/rc/checkbox_checked.png");
}


QListView::indicator:checked:hover,
QListView::indicator:checked:focus,
QListView::indicator:checked:pressed,
QTableView::indicator:checked:hover,
QTableView::indicator:checked:focus,
QTableView::indicator:checked:pressed,
QColumnView::indicator:checked:hover,
QColumnView::indicator:checked:focus,
QColumnView::indicator:checked:pressed {
  image: url(":/qss_icons/dark/rc/checkbox_checked_focus.png");
}

QListView::indicator:unchecked,
QTableView::indicator:unchecked,
QColumnView::indicator:unchecked {
  image: url(":/qss_icons/dark/rc/checkbox_unchecked.png");
}


QListView::indicator:unchecked:hover,
QListView::indicator:unchecked:focus,
QListView::indicator:unchecked:pressed,
QTableView::indicator:unchecked:hover,
QTableView::indicator:unchecked:focus,
QTableView::indicator:unchecked:pressed,
QColumnView::indicator:unchecked:hover,
QColumnView::indicator:unchecked:focus,
QColumnView::indicator:unchecked:pressed {
  image: url(":/qss_icons/dark/rc/checkbox_unchecked_focus.png");
}

QListView::indicator:indeterminate,
QTableView::indicator:indeterminate,
QColumnView::indicator:indeterminate {
  image: url(":/qss_icons/dark/rc/checkbox_indeterminate.png");
}

QListView::indicator:indeterminate:hover,
QListView::indicator:indeterminate:focus,
QListView::indicator:indeterminate:pressed,
QTableView::indicator:indeterminate:hover,
QTableView::indicator:indeterminate:focus,
QTableView::indicator:indeterminate:pressed,
QColumnView::indicator:indeterminate:hover,
QColumnView::indicator:indeterminate:focus,
QColumnView::indicator:indeterminate:pressed {
  image: url(":/qss_icons/dark/rc/checkbox_indeterminate_focus.png");
}
QHeaderView::down-arrow {
  background-color: #455364;
  border: none;
  height: 12px;
  width: 12px;
  padding-left: 2px;
  padding-right: 2px;
  image: url(":/qss_icons/dark/rc/arrow_down.png");
}

QHeaderView::up-arrow {
  background-color: #455364;
  border: none;
  height: 12px;
  width: 12px;
  padding-left: 2px;
  padding-right: 2px;
  image: url(":/qss_icons/dark/rc/arrow_up.png");
}
QSplitter::handle:horizontal {
  width: 3px;
}

QSplitter::handle:vertical {
  height: 3px;
}

/*  ------------------------------------------------------------------------  */
/*  Basic widgets  */

QWidget {
  background-color: #2B2B2B;
}


QFrame {
  background-color: #2B2B2B;
  /* background-color: rgb(50, 50, 50); */
  border: 0px;
  border-radius: 0px;
}
QFrame.fill_background {
  background-color: #333333;
  border: 1px solid #333333;
  border-radius: 4px;
}

QSplitter {
  background-color: transparent;
  border: none
}

QStatusBar {
  color: #fff;
  background-color: #1E1E1E;
  border-radius: 0px;
}

QScrollArea,
QStackedWidget,
QWidget > QToolBox,
QToolBox > QWidget,
QTabWidget > QWidget {
  border: none;
}

QTabWidget::pane {
  border: none;
}

QTabWidget {
  background-color: #000;
  border-left: 32px solid #404040;
}
/*  ------------------------------------------------------------------------  */
/*  Inputs  */
QLineEdit,
QTextEdit,
QPlainTextEdit,
QSpinBox,
QDateEdit,
QDateTimeEdit,
QDoubleSpinBox,
QComboBox {
  color: #F4F4F4;
  background-color: #1E1E1E;
  border: 1px solid #1E1E1E;
  border-radius: 0;
  height: 30px;
  padding:0 8px;
  margin:0;
  width:100%;
}
QTextEdit,
QPlainTextEdit {
  padding: 8px 8px;
}
QLineEdit:disabled,
QTextEdit:disabled,
QPlainTextEdit:disabled,
QSpinBox:disabled,
QDateEdit:disabled,
QDateTimeEdit:disabled,
QDoubleSpinBox:disabled,
QComboBox:disabled {
  color: #6F6F6F;
}
QLineEdit:focus,
QTextEdit:focus,
QPlainTextEdit:focus,
QSpinBox:focus,
QDateEdit:focus,
QDateTimeEdit:focus,
QDoubleSpinBox:focus,
QComboBox:focus {
  color: #fff;
  border-color: #0A8FE2;
}
QTextBrowser:read-only:focus{
  border-color:transparent;
}
/*  Spin buttons  */
QDateTimeEdit::up-button,
QDoubleSpinBox::up-button,
QSpinBox::up-button {
  subcontrol-origin: border;
  subcontrol-position: top right;
  width: 20px;
  border-width: 0px;
  margin-right: 5px;
}
QDateTimeEdit::down-button,
QDoubleSpinBox::down-button,
QSpinBox::down-button {
  subcontrol-origin: border;
  subcontrol-position: bottom right;
  width: 20px;
  border-width: 0px;
  border-top-width: 0;
  margin-right: 5px;
}
/*  QComboBox  */
QComboBox::drop-down {
  border: none;
  color: #333333;
  width: 20px;
}
QComboBox::down-arrow {
  margin-right: $size12;
}
QComboBox::down-arrow:focus {
  margin-right: $size12;
}
QComboBox::down-arrow:disabled {
  margin-right: $size12;
}
QComboBox::item{
  height: 28px;
  border: 8px solid transparent;
  color: #fff;
}
QComboBox::item:selected {
  color: #F4F4F4;
  background-color: #575757;
  border-radius: 0px;
}
QComboBox::item:disabled {
  color: #dbdbdb;
}
QComboBox QAbstractItemView {
  background-color: #333333;
  border: 1px solid #1E1E1E;
  border-radius: 4px;
}
QComboBox QAbstractItemView::item:selected:hover {
  background-color: #575757;
}
QDateEdit::down-arrow,
QDateTimeEdit::down-arrow {
  height: 8px;
  width: 8px;
}
/*  ------------------------------------------------------------------------  */
/*  QPushButton  */
QPushButton {
  /* text-transform: uppercase; */
  margin: 0px;
  padding: 0 $size16;
  font-weight: normal;
  border-radius: 2px;
  border:0;
  background-color: #575757;
  color: #F4F4F4;
  min-height: 30px;
  /* max-height: 30px; */
}
QPushButton.QCommandLinkButton{
  min-height: -1;
  max-height: -1;
}
QPushButton:hover {
  background-color: #656565;
}

QPushButton:checked{
  background-color: #0A8FE2;
  border-color: #0A8FE2;
}
QPushButton:checked:hover{
  background-color: #0f9af0;
}
QPushButton:checked:disabled{
  background-color: #cc0A8FE2;
  border-color: #cc0A8FE2;
}
QPushButton:pressed {
  background-color: #575757;
  border-color: #575757;
}
QPushButton:flat {
  margin: 0px;
  border-color: #575757;
  background-color: transparent;
}
QPushButton:flat:hover{
  border-color:#727272;
  background-color:#09ffffff;
}
QPushButton:flat:pressed {
  background-color: #575757;
}
QPushButton:disabled {
  color: #85dbdbdb;
  background-color: #af575757;
}
QPushButton.ButtonPrimary {
  background-color: #0A8FE2;
  min-height: 30px;
  max-height: 30px;
}
QPushButton.ButtonSuccess{
  background-color: #67C23A;
  min-height: 30px;
  max-height: 30px;
}
QPushButton.ButtonInfo{
  background-color: #575757;
  min-height: 30px;
  max-height: 30px;
}
QPushButton.ButtonWarning{
  background-color: #E6A23C;
  min-height: 30px;
  max-height: 30px;
}
QPushButton.ButtonDanger{
  background-color: #F56C6C;
  min-height: 30px;
  max-height: 30px;
}
QPushButton.ButtonPrimary:hover {
  background-color: #0f9af0;
}
QPushButton.ButtonSuccess:hover{
  background-color: #76d348;
}
QPushButton.ButtonInfo:hover{
  background-color: #707070;
}
QPushButton.ButtonWarning:hover{
  background-color: #f7b14a;
}
QPushButton.ButtonDanger:hover{
  background-color: #fc7c7c;
}
QPushButton[text*="完成"]{
  background-color: #0A8FE2;
}
QPushButton[text*="完成"]:hover{
  background-color: #0f9af0;
}
/*  ------------------------------------------------------------------------  */
/*  QRadioButton and QCheckBox labels  */
QRadioButton,
QCheckBox {
  spacing: $size12;
  color: #F4F4F4;
  height: $size36;
  background-color: transparent;
  spacing: 5px;
}
QRadioButton:disabled,
QCheckBox:disabled {
  color: #dbdbdb;
}
QRadioButton:checked:disabled,
QRadioButton:checked:disabled {
  color: #dbdbdb;
}

QRadioButton::indicator,
QCheckBox::indicator {
  width: $size18;
  height: $size18;
  border-radius: 4px;
}
QRadioButton::indicator:unchecked {
  image: url(":/qss_icons/dark/rc/radio_unchecked.png");
}
QRadioButton::indicator:unchecked:hover, 
QRadioButton::indicator:unchecked:focus, 
QRadioButton::indicator:unchecked:pressed {
  border: none;
  outline: none;
  image: url(":/qss_icons/dark/rc/radio_unchecked_focus.png");
}
QRadioButton::indicator:unchecked:disabled {
  image: url(":/qss_icons/dark/rc/radio_unchecked_disabled.png");
}
QRadioButton::indicator:checked {
  border: none;
  outline: none;
  image: url(":/qss_icons/dark/rc/radio_checked_focus.png");
}
QRadioButton::indicator:checked:hover,
QRadioButton::indicator:checked:focus, 
QRadioButton::indicator:checked:pressed {
  border: none;
  outline: none;
  image: url(":/qss_icons/dark/rc/radio_checked_focus.png");
}
QRadioButton::indicator:checked:disabled {
  outline: none;
  image: url(":/qss_icons/dark/rc/radio_checked_disabled.png");
}
QCheckBox::indicator:unchecked {
  image: url(":/qss_icons/dark/rc/checkbox_unchecked.png");
}
QCheckBox::indicator:unchecked:hover, 
QCheckBox::indicator:unchecked:pressed {
  border: none;
  image: url(":/qss_icons/dark/rc/checkbox_unchecked_focus.png");
}
QCheckBox::indicator:unchecked:disabled {
  image: url(":/qss_icons/dark/rc/checkbox_unchecked_disabled.png");
}
QCheckBox::indicator:checked:hover,
QCheckBox::indicator:checked:pressed,
QCheckBox::indicator:checked:focus{
  border: none;
  image: url(":/qss_icons/dark/rc/checkbox_checked_focus.png");
}
QCheckBox::indicator:checked {
  image: url(":/qss_icons/dark/rc/checkbox_checked_focus.png");
}
QCheckBox::indicator:checked:disabled {
  image: url(":/qss_icons/dark/rc/checkbox_checked_disabled.png");
}
QCheckBox::indicator:indeterminate {
  image: url(":/qss_icons/dark/rc/checkbox_indeterminate_focus.png");
}
QCheckBox::indicator:indeterminate:disabled {
  image: url(":/qss_icons/dark/rc/checkbox_indeterminate_disabled.png");
}
QCheckBox::indicator:indeterminate:hover,
QCheckBox::indicator:indeterminate:focus, 
QCheckBox::indicator:indeterminate:pressed {
  image: url(":/qss_icons/dark/rc/checkbox_indeterminate_focus.png");
}
/*  ------------------------------------------------------------------------  */
/*  .QGroupBox  */
.QGroupBox {
  background-color: #2B2B2B;
  border: 1px solid #4C4C4C;
  border-radius: 4px;
}
.QGroupBox {
  padding: 16px;
  font-size: 12px;
  margin-top: 18px;
}
.QGroupBox::title {
  subcontrol-origin: margin;
  subcontrol-position: top left;
  left: 20px;
  padding-left: 8px;
  padding-right: 8px;
  padding-top: 10px;
}
.QGroupBox::indicator {
  width: $size18;
  height: $size18;
  border-radius: 3px;
}
.QGroupBox::indicator:unchecked {
  border: none;
  image: url(":/qss_icons/dark/rc/checkbox_unchecked.png");
}
.QGroupBox::indicator:unchecked:hover,
.QGroupBox::indicator:unchecked:focus,
.QGroupBox::indicator:unchecked:pressed {
  border: none;
  image: url(":/qss_icons/dark/rc/checkbox_unchecked_focus.png");
}
.QGroupBox::indicator:unchecked:disabled {
  image: url(":/qss_icons/dark/rc/checkbox_unchecked_disabled.png");
}

.QGroupBox::indicator:checked {
  border: none;
  image: url(":/qss_icons/dark/rc/checkbox_checked.png");
}

.QGroupBox::indicator:checked:hover,
.QGroupBox::indicator:checked:focus,
.QGroupBox::indicator:checked:pressed {
  border: none;
  image: url(":/qss_icons/dark/rc/checkbox_checked_focus.png");
}
.QGroupBox::indicator:checked:disabled {
  image: url(":/qss_icons/dark/rc/checkbox_checked_disabled.png");
}
/*  ------------------------------------------------------------------------  */
/*  .QMessageBox  */

.QMessageBox QLabel{
  margin: 20 0 10 0;
}

.QMessageBox QLabel#qt_msgboxex_icon_label{
  margin-left: 50;
}

.QMessageBox QLabel#qt_msgbox_label{
  margin-right: 50;
}
.QMessageBox QPushButton{
  border:0;
}
.QMessageBox QPushButton[text*="是"],
.QMessageBox QPushButton[text="全是(A)"],
.QMessageBox QPushButton[text="确定"],
.QMessageBox QPushButton[text="保存"],
.QMessageBox QPushButton[text="全部保存"],
.QMessageBox QPushButton[text="应用"] {
  background-color: #0A8FE2;
}
.QMessageBox QPushButton[text="丢弃"],
.QMessageBox QPushButton[text="中止"]{
  background-color: #F56C6C;
}
.QMessageBox QPushButton[text*="是"]:hover,
.QMessageBox QPushButton[text="全是(A)"]:hover,
.QMessageBox QPushButton[text="确定"]:hover,
.QMessageBox QPushButton[text="保存"]:hover,
.QMessageBox QPushButton[text="全部保存"]:hover,
.QMessageBox QPushButton[text="应用"]:hover {
  background-color: #0f9af0;
}
.QMessageBox QPushButton[text="丢弃"]:hover,
.QMessageBox QPushButton[text="中止"]:hover{
  background-color: #fc7c7c;
}
QDialog QPushButton[text*="是"],
QDialog QPushButton[text="全是(A)"],
QDialog QPushButton[text="确定"],
QDialog QPushButton[text="保存"],
QDialog QPushButton[text="全部保存"],
QDialog QPushButton[text="应用"] {
  background-color: #0A8FE2;
}
QDialog QPushButton[text="丢弃"],
QDialog QPushButton[text="中止"]{
  background-color: #F56C6C;
}
QDialog QPushButton[text*="是"]:hover,
QDialog QPushButton[text="全是(A)"]:hover,
QDialog QPushButton[text="确定"]:hover,
QDialog QPushButton[text="保存"]:hover,
QDialog QPushButton[text="全部保存"]:hover,
QDialog QPushButton[text="应用"]:hover {
  background-color: #0f9af0;
}
QDialog QPushButton[text="丢弃"]:hover,
QDialog QPushButton[text="中止"]:hover{
  background-color: #fc7c7c;
}
/*  ------------------------------------------------------------------------  */
/*  QTabBar  */
.QTabBar{
  text-transform: uppercase;
  font-weight: normal;
  background-color: #000;
}
.QTabBar::tab {
  color: #F4F4F4;
  border: 0px;
  background-color: #404040;
  text-decoration: none;
}
.QTabBar::tab:left{
  border-left: 2px solid transparent;
  margin-bottom: 1px;
}
.QTabBar::tab:left:selected{
  border-right: 1px solid transparent;
}
.QTabBar::tab:left:!selected{
  border-right: 1px solid #000;
}
.QTabBar::tab:left{
  padding: $size16 0;
  width: $size30;
}
.QTabBar::tab:left:selected {
  border-left-color: #0A8FE2;
  background-color: #2B2B2B; 
}
.QTabBar::tab:right:selected {
  border-right-color: #0A8FE2;
  background-color: #2B2B2B; 
}
.QTabBar::tab:!selected:hover{
  border-color: #0072a3;
}
.QTabBar::tab:left:!selected:hover{
  border-left-color: #0072a3;
  border-right-color: #000;
}
.QTabBar::tab:right:!selected:hover{
  border-right-color: #0072a3;
  border-left-color: #000;
}
.QTabBar::tab:top{
  border-top: 2px solid transparent;
}
.QTabBar::tab:bottom{
  border-bottom: 2px solid transparent;
}
.QTabBar::tab:bottom:!last,
.QTabBar::tab:top:!last{
  margin-right: 1px;
}
.QTabBar::tab:bottom,
.QTabBar::tab:top{
  padding: 0 $size16;
  height: $size30;
}
.QTabBar::tab:bottom:selected,
.QTabBar::tab:top:selected {
  color: #fff;
  border-color: #0A8FE2;
  background-color: #2B2B2B;
}
/*  ------------------------------------------------------------------------  */
/*  .GroupButton  */
.GroupButton::tab{
  min-height: 30px;
  max-height: 30px;
  background-color: #575757;
  margin-right:1px;
  padding:0 16px;
}
.GroupButton::tab:middle{
  border:0;
}
.GroupButton::tab:first {
  border-top-left-radius:2;
  border-bottom-left-radius:2;
  border:0;
}
.GroupButton::tab:last {
  border-top-right-radius:2;
  border-bottom-right-radius:2;
  border:0;
}
.GroupButton::tab:hover{
  background-color: #707070;
}
.GroupButton::tab:selected{
  background-color: #00A8F1;
}
.GroupButton::tab:checked:disabled{
  background-color: #cc0A8FE2;
  border-color: #cc0A8FE2;
}
.GroupButton::tab:disabled {
  color: #dbdbdb;
}
/*  ------------------------------------------------------------------------  */
/*  QToolButton  */
QToolButton {
  background-color: #575757;
  border: 0px;
  margin: 0px;
  padding: 0px 16px;
  border:0;
  border-radius:2px;      
}
.QToolButton{
  min-height: 30px;
  max-height: 30px;
}
QToolButton:hover {
  background-color: #656565;
}

QToolButton:pressed {
  background-color: #575757;
}

QToolButton:checked {
  background-color: #575757;
}
QToolButton:disabled {
  color: #dbdbdb;
}
QToolButton::menu-indicator {
  image: url(":/qss_icons/dark/rc/arrow_down.png");
  height: 8px;
  width: 8px;
  top: -12px;
  left: -10px;
}
QToolButton::menu-arrow {
  image: url(":/qss_icons/dark/rc/arrow_down.png");
  height: 8px;
  width: 8px;
}
QToolButton::menu-arrow:hover {
  image: url(":/qss_icons/dark/rc/arrow_down_focus.png");
}
QTabBar QToolButton::left-arrow:enabled,
QDockWidget QTabBar QToolButton::left-arrow:enabled {
  image: url(":/qss_icons/dark/rc/arrow_left.png");
}

QTabBar QToolButton::left-arrow:disabled,
QDockWidget QTabBar QToolButton::left-arrow:disabled {
  image: url(":/qss_icons/dark/rc/arrow_left_disabled.png");
}

QTabBar QToolButton::right-arrow:enabled,
QDockWidget QTabBar QToolButton::right-arrow:enabled {
  image: url(":/qss_icons/dark/rc/arrow_right.png");
}

QTabBar QToolButton::right-arrow:disabled,
QDockWidget QTabBar QToolButton::right-arrow:disabled {
  image: url(":/qss_icons/dark/rc/arrow_right_disabled.png");
}
/*  ------------------------------------------------------------------------  */
/*  General Indicators  */
QMenu::indicator,
QListView::indicator,
QTableWidget::indicator {
  width: $size18;
  height: $size18;
  border-radius: 4px;
}
/*  ------------------------------------------------------------------------  */
/*  QDockWidget  */

QDockWidget {
  color: #fff;
  text-transform: uppercase;
  border: 1px solid #333333;
  border-radius: 4px;
}
QDockWidget::title {
  text-align: left;
  padding-left: $size36;
  padding: 3px;
  margin-top: 4px;
}
/*  ------------------------------------------------------------------------  */
/*  Menu Items  */
QCalendarWidget QMenu::item,
QMenu::item {
  height: 28px;
  border: 8px solid transparent;
  color: #fff;
}
QCalendarWidget QMenu::item,
QMenu::item {
  padding: 0px $size24 0px 8px;
}
QCalendarWidget QMenu::item:selected,
QMenu::item:selected {
  color: #F4F4F4;
  background-color: #404040;
  border-radius: 0px;
}
QCalendarWidget QMenu::item:disabled,
QMenu::item:disabled {
  color: #dbdbdb;
}
/*  ------------------------------------------------------------------------  */
/*  QMenu  */
QCalendarWidget QMenu,
QMenu {
  background-color: #333333;
  border: 1px solid #1E1E1E;
  border-radius: 4px;
}
QMenu::separator {
  height: 2px;
  background-color: #1E1E1E;
  margin-left: 2px;
  margin-right: 2px;
}
QMenu::right-arrow{
  width: $size16;
  height: $size16;
}
/*  ------------------------------------------------------------------------  */
/*  QMenuBar  */
QMenuBar {
  background-color: #333333;
  color: #fff;
}
QMenuBar::item {
  height: $size30;
  padding: 8px;
  background-color: transparent;
  color: #fff;
}
QMenuBar::item:selected,
QMenuBar::item:pressed {
  color: #F4F4F4;
  background-color: #404040;
}
/*  ------------------------------------------------------------------------  */
/*  QToolBox  */
QToolBox::tab {
  background-color: #333333;
  color: #fff;
  text-transform: uppercase;
  border-radius: 0px;
  padding-left: 15px;
}
QToolBox::tab:selected,
QToolBox::tab:hover {
  background-color: #33656565;
}
/*  ------------------------------------------------------------------------  */
/*  QProgressBar  */
QProgressBar {
  border-radius: 0;
  background-color: #1E1E1E;
  text-align: center;
  color: transparent;
}
QProgressBar::chunk {
  background-color: #0A8FE2;
}
/*  ------------------------------------------------------------------------  */
/*  QScrollBar  */
QScrollBar {
  border: 0;
  background: #1c000000;
  width: 8px;
  height: 8px;
}
QScrollBar::handle {
  background: #333333;
}
QScrollBar::handle:horizontal {
  min-width: $size24;
}
QScrollBar::handle:vertical {
  min-height: $size24;
}
QScrollBar::handle:hover {
  background: #3b3b3b;
  
}
QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical,
QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
  border: 0;
  background: transparent;
  width: 0px;
  height: 0px;
}

QScrollBar::sub-page:horizontal,
QScrollBar::add-page:horizontal,
QScrollBar::sub-page:vertical,
QScrollBar::add-page:vertical,
QScrollBar:vertical {
    background: transparent;
}
/*  ------------------------------------------------------------------------  */
/*  QSlider  */

QSlider:horizontal {
  min-height: $size24;
  max-height: $size24;
}

QSlider:vertical {
  min-width: $size24;
  max-width: $size24;
}

QSlider::groove:horizontal {
  height: 4px;
  background-color: #1E1E1E;
  margin: 0;
}

QSlider::groove:vertical {
  width: 4px;
  background: #1E1E1E;
  margin: $size12 0;
}

QSlider::handle:horizontal {

  width: $size18;
  height: $size18;
  margin: -$size18 -9px;
}

QSlider::handle:vertical {

  width: $size18;
  height: $size18;
  margin: -9px -$size18;
}

QSlider::add-page {
  background: #1E1E1E;
}

QSlider::sub-page {
background: #0A8FE2;
}

/*  ------------------------------------------------------------------------  */
/*  .QLabel  */

.QLabel {
  border: none;
  background: transparent;
  color: #fff
}

.QLabel:disabled {
  color: #6F6F6F
}

/*  ------------------------------------------------------------------------  */
/*  VLines and HLinex  */
QFrame[frameShape="4"],
QFrame[frameShape="5"]{
  border-style: solid;
  border-color: #1E1E1E;
  background: none;
}
QFrame[frameShape="4"] {
  border-width: 1px 0 0 0;
}

QFrame[frameShape="5"] {
  border-width: 0 0 0 1px;
}
/*  ------------------------------------------------------------------------  */
/*  QToolBar  */

QToolBar {
  background: #2B2B2B;
  border: 0px solid;
}

QToolBar:horizontal {
  border-bottom: 1px solid #1E1E1E;
}

QToolBar:vertical {
  border-right: 1px solid #1E1E1E;
}

QToolBar::separator:horizontal {
  border-right: 1px solid #1E1E1E;
  border-left: 1px solid #1E1E1E;
  width: 1px;
}

QToolBar::separator:vertical {
  border-top: 1px solid #1E1E1E;
  border-bottom: 1px solid #1E1E1E;
  height: 1px;
}
/*  ------------------------------------------------------------------------  */
/*  QTreeView */
QTreeView{
  color: #F4F4F4;
  border-radius: 0px;
  padding: 0px;
  margin: 0px;
  /* border:1px solid #4C4C4C; */
  background-color: #1E1E1E;
  alternate-background-color: #272727;
}
QTreeView::item{
  padding: 4px;
  min-height: 22px;
  border:0px;
}
QTreeView QHeaderView::section:horizontal {
  border-left-width:0px;
  border-top-width:0px;
  padding-top:5px;
}
QTreeView QHeaderView::section:last {
  border-right-width:0px;
}
QTreeView::item:hover{
  background-color: #313131;
}
QTreeView::item:selected {
  background-color: #353535;
}
QTreeView::branch:hover{
  background-color: #404040;
}
/** QTreeView 指示器 **/
QTreeView::indicator {  
  width: 30px;
  height: 30px;
}

QTreeView::indicator:enabled:unchecked { 
  image: url("./res/icon/checkbox/unchecked_0.png");
}
QTreeView::indicator:enabled:unchecked:hover,
QTreeView::indicator:enabled:unchecked:pressed {
  image: url("./res/icon/checkbox/unchecked_1.png");
}
QTreeView::indicator:disabled:unchecked  {
  image: url("./res/icon/checkbox/unchecked_2.png");
}
QTreeView::indicator:enabled:checked,
QTreeView::indicator:enabled:checked:hover,
QTreeView::indicator:enabled:checked:pressed {
  image: url("./res/icon/checkbox/checked_1.png");
}
QTreeView::indicator:disabled:checked  {
  image: url("./res/icon/checkbox/checked_2.png");
}

QTableView::indicator:enabled:indeterminate,
QTreeView::indicator:enabled:indeterminate:hover,
QTreeView::indicator:enabled:indeterminate:pressed {
  image: url("./res/icon/checkbox/indeterminate_1.png");
}
QTreeView::indicator:disabled:indeterminate  {
  image: url("./res/icon/checkbox/indeterminate_2.png");
}
/*  ------------------------------------------------------------------------  */
/*  QTableView  */
QTableView {
  border-radius: 0px;
  gridline-color: #000;
  border:1px solid #000;
  background-color: #1E1E1E;
  alternate-background-color: #333333;
}
QTableView::item{
  padding: 4px;
  min-height: $size30;
  border:0px;
}
QTableView::item:selected {
  background-color: #565656;
  selection-background-color: #565656;
}

QTableView::item:selected:focus{
  background-color: #565656;
  selection-background-color: #565656;
}
QTableView QLineEdit{
  padding:0px 4px;
}
/*  ------------------------------------------------------------------------  */
/*  QListView  */
QListView {
  color: #F4F4F4;
  border-radius: 0px;
  padding: 0px;
  margin: 0px;
  border:1px solid transparent;
  background-color: #2B2B2B;
  alternate-background-color: #333333;
}
QListView::item {
  padding: 4px;
  border:0px;
  margin-bottom: 1px;
}
QListView::item:selected,
QListView::item:selected:focus {
  background-color: #464646;
  selection-background-color: #464646;
}
QListView QLineEdit{
  padding:0px 4px;
}
/*  ------------------------------------------------------------------------  */
/*  Items Selection */
QTableCornerButton::section {
  background-color: #363636;
  border-radius: 0px;
  border:1px solid #000;
  border-left-width:0px;
  border-top-width:0px;
}
QHeaderView {
  gridline-color: #4C4C4C;
  font-weight:bold;
  color: #9c9c9c;
}
QHeaderView::section:vertical {
  background-color:#363636;
  border:1px solid #000;     
  border-left-width:0px;
  border-top-width:0px;
  padding-left:5px;
}

QHeaderView::section:horizontal {
  background-color:#363636;
  border:1px solid #000;     
  border-left-width:0px;
  border-top-width:0px;
  padding-left:10px;
  padding-top:2px;
}
/*  ------------------------------------------------------------------------  */
/*  QLCDNumber  */

QLCDNumber {
  color: #333333;
  background-color:#1a656565;
  border: 1px solid #4d656565;
  border-radius: 4px;
}
/*  ------------------------------------------------------------------------  */
/*  QCalendarWidget  */

QCalendarWidget {
  min-height: 300px;
}
/*  ------------------------------------------------------------------------  */
/*  QToolTip  */
QToolTip {
  padding: 4px;
  border: 1px solid #2B2B2B;
  /* border-radius: 4px; */
  color: #fff;
  background-color: #1E1E1E;
}
/*  ------------------------------------------------------------------------  */
/*  Grips  */
QMainWindow::separator:vertical,
QSplitter::handle:horizontal {
  background-color: #000000;
}
QMainWindow::separator:horizontal,
QSplitter::handle:vertical {
  background-color: #000000;
}
QSizeGrip {
  background-color: transparent;
}
/*  ------------------------------------------------------------------------  */
QMenu::indicator:focus,
QListView::indicator:focus,
QTableWidget::indicator:focus{
  background-color: #33656565;
  border-radius: 14px;
}
/** UiPjNewGrid 欢迎页 */
uiWelcome .QLabel#welcome{
  padding: 16px 8px 2px;
  font-size: 14px;
}
uiWelcome QPushButton:disabled {
  background-color: #383838;
  color:#CACACA;
  border:0;
  font-size: 12px;
  margin-bottom: 0px;
  font-weight:normal;
}
uiWelcome UiPjNewGrid .QLabel {
  color: #D2D6D9;
  border:0;
  font-size: 16px;
}
LabelH3 {
  font-size: 16px;
  padding-left: 8px;
}
LabelH2 {
  padding: 16px 8px 2px;
  font-size: 24px;
}
uiWelcome QPushButton#splitter{
  width:1px;
  height:600px;
  padding:0;
  background-color: rgba(80, 80, 80, 1);
  margin:50px 20px 0 40px;
  border:0;
}
WelcomeIconButton {
  width:100px;
  height:90px;
  background-repeat: no-repeat;
  background-color:transparent;
  background-position:top center;
  font-size: 14px;
  color: #D2D6D9;
  text-align: bottom;
  padding-bottom:10px;
  border:0;
  font-weight:normal;
  margin-top:40px;
}
WelcomeIconButton:hover{
  background-color: #383838;
}
WelcomeIconButton[text="操作设备"] {
  background-image: url("./res/icon/welcome/Frame-0.png");
}
WelcomeIconButton[text="分析文件"] {
  background-image: url("./res/icon/welcome/Frame-1.png"); 
}
WelcomeIconButton[text="执行测试"] {
  background-image: url("./res/icon/welcome/Frame-2.png"); 
}
WelcomeIconButton[text="打开工程"] {
  background-image: url("./res/icon/welcome/Frame-3.png");
}

UiRichList UiRichListItem {
  background-color: transparent;
  border:0;
  height: 60px;
}
UiRichList UiRichListItem:hover { 
  background-color: #383838;
}
UiRichList LabelIcon {
  background-color: transparent;
  min-width:50px;
  max-width:50px;
  min-height:50px;
  max-height:50px;
}
UiRichList LabelTitle {
  margin: 0;
  padding:0;
  background-color: transparent;
}
UiRichList LabelInfo {
  color: rgba(198, 198, 198, 1);
  margin:0;
  padding:0;
  font-size: 12px;
  background-color: transparent;
}
uiWelcome LabelH3 {
  margin-top: 50px;
  margin-bottom: 20px;
}
/** QNavigationWidget 左侧菜单 */
QNavigationWidget QListWidget {
  background-color: #252525;
  padding:0;
  font-size: 14px;
  border:0;
  border-top:1px solid #2B2B2B;
}
QNavigationWidget QListWidget::item {
  background-color: transparent;
  height:30px;
  margin-bottom:8px;
  padding-left:20px;
  color:#C6C6C6;
  border-left: 2px solid transparent;
}
QNavigationWidget QListWidget::item:!focus:hover,
QNavigationWidget QListWidget::item:focus:hover{
  background-color: #6b404040;
  border-color: #0072a3;
}
QNavigationWidget QListWidget::item:!focus:selected,
QNavigationWidget QListWidget::item:focus:selected {
  background-color: #404040;
  border-color:#00A8F1;
  color:#fff;
}
QNavigationWidget QListWidget QPushButton {
  font-size: 30px;
  background-color: #ff0000;
  background-size: 100px;
}
/** UiProject 左侧菜单详情页 */
UiProject UiPjInfo .QLabel {
  padding-left: 48px;
  color:#C6C6C6;
}
UiProject UiPjInfo LabelH2 {
  padding: 36px 42px;
}
UiProject UiPjInfo QTextEdit {
  margin-left: 32px;
  max-width:300px;
  min-width:300px;
}
UiProject UiPjNew LabelH2 {
  padding-left: 0px;
  padding-top: 36px;
  color:#C6C6C6;
}

UiProject UiPjOpen LabelH2 {
  padding-left: 0px;
  padding-top: 36px;
  color:#C6C6C6;
}

UiProject UiPjOpen .QPushButton:!flat{
  max-width:164px;
  padding:5px 0;
  margin:10px 10px 30px;
}

UiProject UiPjOption .QLabel {
  padding-left: 42px;
  color:#C6C6C6;
}
UiProject UiPjOption LabelH2 {
  padding-left: 36px;
  padding-top: 36px;
  padding-bottom: 36px;
  color:#C6C6C6;
}
UiProject UiPjOption QLineEdit,
UiProject UiPjOption QComboBox {
  max-width:300px;
  min-width:300px;
}

/** ToolPage 弹窗 */
ToolPage{
  background-color:#1E1E1E;
  padding: 0;
}
ToolBoxButton{
  background-color: #404040;
  border:0;
  border-radius:0;
  margin-bottom:1px;
  text-align:left;
  min-height:30px;
}
ToolBoxButton:hover{
  background-color: #343434;
}
ToolBoxIcon{
  max-width:14px;
  max-height:14px;
  margin-right:8px;
  background-color:transparent;
}
EquipmentItem{
  min-width:180px;
  min-height:160px;
  padding:0;
  border:0;
  margin:1px;
  border-radius:0;
  background-color: #49404040;
}
EquipmentItem:hover,
EquipmentItem:checked,
EquipmentItem:checked:hover,
EquipmentItem:selected:hover,
EquipmentItem:focus:hover {
  background-color: #404040;
}

EquipmentItem LabelIcon{
  min-width:164px;
  max-width:164px;
  min-height:88px;
  max-height:88px;
}
EquipmentItem LabelIcon,
EquipmentItem LabelTitle,
EquipmentItem LabelInfo{
  background-color: transparent;
}
EquipmentFlow{
  background-color: #1E1E1E;
}
/** PropEquipment 属性面板 */
PropEquipment {
  background-color: #2B2B2B;
  padding:0;
  margin:0;
}
PropEquipment >QWidget{
  margin:0;
  padding:0;
  background-color: transparent;
}
PropEquipment ToolBoxContent QLabel{
  min-width:80px;
}
PropEquipment ToolBoxContent QPushButton{
  padding:0 8px;
}
PropEquipment ToolBoxContent SwitchButton{
  padding:0;
}
SwitchButton{
  background:#4A4A4A; 
  border: none;
  border-radius: 10px;  
  min-height: 20px;
  max-height: 20px;
  max-width: 40px;
  min-width: 40px;
  padding: 0;
  color:#fff;
  margin:0;
}
SwitchButton:hover{
  background:#4A4A4A; 
}
SwitchButton:checked{
  background: #0A8FE2;
}

SwitchButton::handle{
  background: #747474;
  border:5px solid transparent;
  max-width:10px;
  min-width:10px;
  max-height:10px;
  min-height:10px;
  border-radius:10px;
}
SwitchButton::handle:checked{
  background: #fff;
}
ColorButton{
  min-width:80px;
  max-width:80px;
  min-height:30px;
  max-height:30px;
  background:transparent;
  border:1px solid #fd575757;
}
ColorButton QLabel{
  margin:5px;
}
ColorButton:hover{
  background-color:#09ffffff;
  border-color:#727272;
}
ButtonEdit{
  padding:0 12px;
}
ButtonEdit QPushButton{
  padding:0 10px;
  border:0;
  background-color:#b04a4a4a;
  border-left:1px solid#8f4a4a4a;
  border-radius:0px;
  margin-top: 0px;
  margin-bottom:0;
  margin-right:1px;
  height:30px;
  min-width: 24px;
  max-width: 24px;
}
ButtonEdit QPushButton:flat{
  margin-top: -1px;
  margin-bottom:-1px;
  margin-right:1px;
  background-color:#8f4a4a4a;
  border-left:1px solid#8f4a4a4a;
}
ButtonEdit QPushButton:hover,
ButtonEdit QPushButton:flat:hover{
  background-color:#d24a4a4a;
  border-left:1px solid#8f4a4a4a;
}
FixEdit {
  padding: 0 8px;
}
FixEdit QLabel{
  padding:0 8px;
}
/** UiEquipmentList  设备列表 */
UiEquipmentList::item {
  height: 160px;
  background-color:#383838;
  padding:0 0 10px;
  margin-bottom:7px;
}
UiEquipmentList::item:selected:focus,
UiEquipmentList::item:selected:!focus{
  background-color:#414141;
}
UiEquipmentListItem{
  background: transparent;
  margin: 0;
  padding: 0px;
}
UiEquipmentListItemBar QPushButton{
  min-width:20px;
  max-width:20px;
  padding: 0px;
  border-radius:10px;
}
UiEquipmentListItemBar QPushButton#Connect{
  min-height:20px;
  max-height:20px;
  background: url("./res/icon/widget/connect.png") no-repeat center center;
  background-color:#494949;
}
UiEquipmentListItemBar QPushButton#Connect:checked{
  background-image: url("./res/icon/widget/deconnect.png");
  background-color: #494949;
  border:0;
}
UiEquipmentListItem QPushButton#Delete{
  min-height:20px;
  max-height:20px;
  background: url("./res/icon/widget/delete.png") no-repeat center center;
  background-color: #494949;
}
UiEquipmentListItemBar QPushButton#Connect:hover,
UiEquipmentListItemBar QPushButton#Delete:hover{
  background-color:#6b808080;

}
UiEquipmentListItemRect{
  min-height: 120px;
  margin-top:10px;
  background-color:transparent;
  padding:10px;
}

UiEquipmentListItemRect UiEquipmentListItemBar{
  min-height: 14px;
  max-height: 14px;
  padding:10px 0 0;
}

UiEquipmentListItemRect LabelIcon{
  background-color:#0cffffff;
  max-height:88px;
  margin-bottom:0;
}
UiEquipmentListItemRect LabelTitle{
  background-color:transparent;
  max-height:14px;
}
PanelTest QListWidget::item {
  background-color:#383838;
  min-height:30px;
  max-height:30px;

}
/** 设备波形图 */
UiEquipWave QToolBar,
UiTestWave QToolBar{
  background-color: #404040;
}
UiEquipWave QToolBar QLabel,
UiTestWave QToolBar QLabel{
  margin-left:10px;
}
UiEquipWave QToolBar QWidget#Blank,
UiTestWave QToolBar QWidget#Blank {
  background-color:transparent;

}
UiEquipWave QToolBar QToolButton,
UiTestWave QToolBar QToolButton,
UiData QToolBar QToolButton {
  background-color: transparent;
  border-radius:none;
  max-width:20px;
  min-width:20px;
  padding:0 12px;
}
UiEquipWave QToolBar::separator,
UiTestWave QToolBar::separator {
  background-color: #2B2B2B;
  width:0px;
  height: 3px;
  border:none;
  padding:0;
  margin:5px -1px;
}
UiEquipWave QToolBar QToolButton[text],
UiTestWave QToolBar QToolButton[text]{
  max-width:-1;
}
UiData QToolBar{
  background-color: #404040;
}
UiData QToolBar QLabel{
  margin-left:10px;
}
UiData QToolBar QWidget#Blank {
  background-color:transparent;
}
UiData QToolBar QToolButton[text]{
  max-width:-1;
}
UiData {
  padding-left:10px;
}
QWidget#MainWnd{
  background-color: #000;
}
QWidget#CenWnd {
  background-color: red;
}
DragTreeWidget QLabel#Drag{
  background-color:rgba(255,255,255,0.2);
  padding:6px 12px;
}
/* 通道 PropChannel */
PropChannel IconButton {
  min-width:-1;
  max-width:-1;
  min-height:-1;
  max-height:-1;
  padding:0;
  margin:-7px auto;
  background-color:transparent;
  border:0;
}
PropChannel IconButton:hover{
  background-color: transparent;
}
PropChannel ColorButton {
  margin:0;
  min-height:-1;
  min-width:-1;
  max-width:-1;
  border:0;
}
PropChannel ColorButton QLabel{
  margin:0;
}
/** 面板内部状态栏 */
UiStatus{
  border-top: 1px solid #000;
  min-height:40px;
  padding-right:10px;
}

UiStatus .QLabel {
 color:#eee;
}
/** footer 底部状态栏 */
QStatusBar {
  background-color:#4D4D4D;
}
QStatusBar QFrame[frameShape="5"]{
  border-color:#000;
  margin: 2px 0;
}
QStatusBar::item{
  border:0;
  padding:0;
  margin:0;
}
QStatusBar .QLabel{
  color:#B3B3B3;
  padding:0 5px 0 3px;
  background:transparent;
  max-height:16px;
}
QStatusBar QPushButton{
  border-color:transparent;
  background-color:transparent; 
  padding:0;
  margin:0;
  max-height:20px;
  min-height:20px;
}
QStatusBar QPushButton:hover{
  border-color:transparent;
}
