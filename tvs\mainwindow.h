#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QMap>

#include "SARibbonMainWindow.h"
#include "DockManager.h"
#include "DockWidget.h"
#include "app/DocFile.h"

QT_BEGIN_NAMESPACE
namespace Ui
{
class MainWindow;
}
QT_END_NAMESPACE

class ThemeEditor;
class QNavigationWidget;
class SARibbonActionsManager;
class SlidingStackedWidget;
class QFileSystemWatcher;
class UiProject;
class UiEquipment;
class PanelEquipment;
class PanelTest;

namespace ads
{
class CDockWidget;
}

namespace QCPL
{
    class GraphDataGrid;
}
class Graph;
class PlotWidget;


//class MainWindow : public QMainWindow
class MainWindow : public SARibbonMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();


public:
    static MainWindow * instance;

    // 菜单操作
    void showCategory(const QString &title, bool on);
    void showContextCategory(const QString & title, bool on);
    void setActionChecked(const QString & title, bool checked);

    // dock面板操作
    void setPanelWidth();
    void setDockStack();
    void setDockTile();

    // 工程打开、保存、另存
    void openProject();
    void saveProject();
    void saveProjectAs();

    // 文档保存、加载
    bool saveDoc(const QString & file="");
    bool loadDoc(const QString & file);

    // 新建设备、测试
    void newEquipment();
    void newTest();

    // 添加新设备
    void addNewEquipment(int type, const QString &name, const QString &ip, const QString &port);
    // 添加新测试
    void addNewTest(int type, const QString &name);

    // 显示主页面
    void ActivateMainUI();

    // 显示工程页面
    void ActivateProjectUI();

    // 激活设备视图
    void ActivateEquipmentView(const QString& id, const QString & title);
    // 激活测试视图
    void ActivateTestView(const QString& id, const QString & title);
    // 关闭、删除(可选)设备视图
    void DeactivateTestView(const QString& id, bool bDelete);
    // 关闭、删除(可选)测试视图
    void DeactivateEquipmentView(const QString& id, bool bDelete);

    // 删除设备
    void DeleteEquipment(const QString& id);
    // 删除测试
    void DeleteTest(const QString& id);

    // 返回所有打开的dock
    QList<ads::CDockWidget*> getAllOpenDocks();

    // 连接设备
    void connectEquipment(const QString& id);
    // 断开设备
    void deconnectEquipment(const QString& id);

    void graphSelected(PlotWidget *plot, Graph *graph);

    void tvms_start();
    void tvms_stop();

private slots:
    void onActionVisibleAllTriggered(bool on);
    void onQssChanged(const QString & path);


signals:
    void recentFilesChanged(QStringList);
    void equipmentConnectChanged(QString, bool);

private:
    void initRibbonBar();
    void initDockWidget();
    void initQssWatcher();
    void registerCommand();

    void initRibbonByJson();
    void initLeftDockWidget();
    void initRightDockWidget();
    void showDlg();
    void initThemeEditor();

    void addRecentFile(const QString &file);
    QAction *createAction(const QString &text, const QString &iconurl);

public:
    DocFile m_doc;
    QStringList m_recent_files;
    const int MaxRecentFiles = 10;

private:
    Ui::MainWindow *ui;
    SlidingStackedWidget *m_mainStack;
    UiProject *m_project_widget;

    QCPL::GraphDataGrid *m_dataGrid;


    ads::CDockManager* m_dock_manager;
    ads::CDockManager* m_cen_dock_manager;

    ads::CDockWidget* m_dock_cen;
    ads::CDockWidget* m_dock_left;
    ads::CDockWidget* m_dock_right;

    QTabWidget* m_panel_tab;
    PanelEquipment* m_panel_equip;
    PanelTest* m_panel_test;

    QMap<QString, ads::CDockWidget*> m_map_id_equipmentview;
    QMap<QString, ads::CDockWidget*> m_map_id_testview;

    UiEquipment * m_cur_equipment;
    QTabWidget *m_right_tab;

    SARibbonActionsManager* m_ActionsManager;
    QAction* m_ActionVisibleAll;

    QFileSystemWatcher* m_pFileWatcher;

    // 开发测试用，可删
    ThemeEditor* m_editor_widget;
    ThemeEditor* m_editor_dock;
    ThemeEditor* m_editor_rb;

    // QWidget interface
protected:
    virtual void resizeEvent(QResizeEvent *event) override;
    virtual void closeEvent(QCloseEvent *event) override;
    virtual void changeEvent(QEvent *) override;
};

#endif // MAINWINDOW_H
