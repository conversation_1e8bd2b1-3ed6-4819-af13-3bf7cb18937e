#ifndef APPCONFIG_H
#define APPCONFIG_H

#include "utils/singleton.h"
#include <QString>
#include <QDir>
#include <QCoreApplication>

/**
 * @brief The AppConfig class manages application-wide configuration settings.
 * 
 * This class provides access to configuration values loaded from a YAML file.
 * It follows the singleton pattern to ensure a single source of truth for configuration.
 */
class AppConfig : public Singleton<AppConfig>
{
public:
    /**
     * @brief Get the application name
     * @return Application name as a QString
     */
    QString appName() const { return m_appName; }
    
    /**
     * @brief Get the company name
     * @return Company name as a QString
     */
    QString companyName() const { return m_companyName; }
    
    /**
     * @brief Get the main window title
     * @return Window title as a QString
     */
    QString windowTitle() const { return m_windowTitle; }
    
    /**
     * @brief Load configuration from a YAML file
     * @param configPath Path to the configuration file. If empty, uses default path.
     * @return true if configuration was loaded successfully, false otherwise
     */
    bool loadConfig(const QString &configPath = QString());

private:
    friend class Singleton<AppConfig>;
    
    /**
     * @brief Private constructor
     */
    AppConfig();
    
    // Configuration values
    QString m_appName = "TVS";
    QString m_companyName = "Qingzhi Instruments";
    QString m_windowTitle = "青智仪器";
    
    /**
     * @brief Get the default configuration file path
     * @return Path to the default configuration file
     */
    QString getDefaultConfigPath() const;
};

// Note: APP_CONFIG is a reference, so use the dot (.) operator, not arrow (->)
#define APP_CONFIG (AppConfig::instance())

#endif // APPCONFIG_H
