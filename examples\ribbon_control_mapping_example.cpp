/**
 * @file ribbon_control_mapping_example.cpp
 * @brief SARibbonMainWindowJson 使用示例
 * 
 * 这个示例展示了如何使用 SARibbonMainWindowJson 基类来实现
 * 基于 JSON 配置的 Ribbon 界面，并使用控件映射功能。
 */

#include "SARibbonMainWindowJson.h"
#include "SARibbonToolButton.h"
#include <QApplication>
#include <QTimer>
#include <QDebug>

class ExampleMainWindow : public SARibbonMainWindowJson
{
    Q_OBJECT

public:
    ExampleMainWindow(QWidget *parent = nullptr)
        : SARibbonMainWindowJson(parent)
    {
        setupUI();
        registerCommands();
    }

private slots:
    void onDemoButtonClicked()
    {
        // 演示控件映射功能
        qDebug() << "=== 控件映射功能演示 ===";
        
        // 1. 检查控件是否存在
        if (hasControl("sta_operate_newequipment")) {
            qDebug() << "找到 'sta_operate_newequipment' 控件";
            
            // 2. 获取控件并操作
            auto toolButton = getRibbonToolButton("sta_operate_newequipment");
            if (toolButton) {
                qDebug() << "控件文本：" << toolButton->text();
                qDebug() << "当前启用状态：" << toolButton->isEnabled();
                
                // 3. 使用便捷方法操作控件
                enableControlByCmd("sta_operate_newequipment", false);
                qDebug() << "已禁用控件";
                
                // 4. 延迟恢复
                QTimer::singleShot(2000, [this]() {
                    enableControlByCmd("sta_operate_newequipment", true);
                    qDebug() << "已恢复控件启用状态";
                });
            }
        } else {
            qDebug() << "未找到 'sta_operate_newequipment' 控件";
        }
        
        // 5. 演示批量操作
        QStringList cmdList = {"sta_operate_importfile", "sta_operate_newtest"};
        for (const QString& cmd : cmdList) {
            if (hasControl(cmd)) {
                auto button = getRibbonToolButton(cmd);
                if (button) {
                    qDebug() << "控件" << cmd << "文本：" << button->text();
                }
            }
        }
    }

protected:
    // 重写 createAction 方法，添加自定义逻辑
    virtual QAction* createAction(const QString& text, const QString& iconPath) override
    {
        QAction* action = SARibbonMainWindowJson::createAction(text, iconPath);
        action->setStatusTip(QString("Action: %1").arg(text));
        return action;
    }
    
    // 重写 processCustomControlType 方法，支持自定义控件类型
    virtual bool processCustomControlType(const QString& type, const QJsonObject& actionObj, SARibbonPannel* pannel) override
    {
        if (type == "demo_button") {
            // 创建自定义按钮
            auto button = new QPushButton(actionObj.value("title").toString());
            connect(button, &QPushButton::clicked, this, &ExampleMainWindow::onDemoButtonClicked);
            
            pannel->addLargeWidget(button);
            
            // 注册到映射
            auto cmd = actionObj.value("cmd").toString();
            if (!cmd.isEmpty()) {
                registerControl(cmd, button);
            }
            
            return true;
        }
        
        // 调用基类处理其他类型
        return SARibbonMainWindowJson::processCustomControlType(type, actionObj, pannel);
    }

private:
    void setupUI()
    {
        setWindowTitle("Ribbon 控件映射示例");
        resize(1200, 800);
        
        // 从 JSON 文件初始化 Ribbon
        if (!initRibbonByJson("./config/ribbon.json")) {
            qDebug() << "警告：无法加载 ribbon.json，将创建默认 Ribbon";
            createDefaultRibbon();
        }
    }
    
    void createDefaultRibbon()
    {
        // 如果 JSON 文件不存在，创建一个简单的默认 Ribbon
        auto ribbon = ribbonBar();
        auto category = ribbon->addCategoryPage("示例");
        auto pannel = category->addPannel("操作");
        
        // 添加一些示例按钮
        auto action1 = createAction("新建设备", "");
        pannel->addLargeAction(action1);
        registerControl("sta_operate_newequipment", pannel->actionToRibbonToolButton(action1));
        
        auto action2 = createAction("导入文件", "");
        pannel->addLargeAction(action2);
        registerControl("sta_operate_importfile", pannel->actionToRibbonToolButton(action2));
        
        auto action3 = createAction("新建测试", "");
        pannel->addLargeAction(action3);
        registerControl("sta_operate_newtest", pannel->actionToRibbonToolButton(action3));
        
        // 添加演示按钮
        auto demoButton = new QPushButton("演示控件映射");
        connect(demoButton, &QPushButton::clicked, this, &ExampleMainWindow::onDemoButtonClicked);
        pannel->addLargeWidget(demoButton);
        registerControl("demo_button", demoButton);
    }
    
    void registerCommands()
    {
        // 注册命令处理器
        ActionManager::instance().registerCommand("sta_operate_newequipment", [this](bool) {
            qDebug() << "执行新建设备命令";
        });
        
        ActionManager::instance().registerCommand("sta_operate_importfile", [this](bool) {
            qDebug() << "执行导入文件命令";
        });
        
        ActionManager::instance().registerCommand("sta_operate_newtest", [this](bool) {
            qDebug() << "执行新建测试命令";
        });
    }
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    ExampleMainWindow window;
    window.show();
    
    return app.exec();
}

#include "ribbon_control_mapping_example.moc"
