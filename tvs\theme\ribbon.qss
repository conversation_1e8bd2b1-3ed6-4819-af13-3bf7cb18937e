/* 
start ribbon set
dark style theme 2
参考AutoCAD 2021的界面配色

深色背景颜色:#2B2B2B
浅色背景颜色:#404040
更浅一层背景颜色:#2B2B2B 一般是控件的编辑区域颜色
字体颜色:#C6C6C6
按钮背景颜色：#404040
按钮hover背景:#1E1E1E
选中边框颜色:#2a8db5
选中按钮上边框颜色: #0A8FE2
*/

/*SARibbonBar*/
SARibbonBar{
  background-color: #404040;
  border: solid #404040;
  color:#C6C6C6;
}
SARibbonBar .QToolButton::left-arrow{
  background:url("./res/icon/ribbon/left-arrow.png") no-repeat center center;
}

SARibbonBar .QToolButton::right-arrow{
  background:url("./res/icon/ribbon/right-arrow.png") no-repeat center center;
}
/*SARibbonQuickAccessBar*/
SARibbonQuickAccessBar{
   background-color: transparent;
}
/*SARibbonCtrlContainer*/
SARibbonCtrlContainer {
  background-color: transparent;
}

/*
SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
*/
SARibbonSeparatorWidget {
  color: #5d636a;
}

/*SARibbonCategory*/
SARibbonCategory {
  background-color: #2B2B2B;
  /*顶部有一条线*/
  border:0px;
}
SARibbonCategory:focus {
  outline: none;
}

/*
SARibbonCategory下的SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
*/
SARibbonCategory > SARibbonSeparatorWidget {
  color: transparent;
}

/*SARibbonPannel*/

SARibbonPannel {
  background-color: #2B2B2B;
  border: none;
  border-right: 1px solid rgba(255, 255, 255, 0.10);
  margin:8px 0;
}
SARibbonCategory QFrame[frameShape="5"]{
  border: none;
  background: none;
}

/*SARibbonToolButton*/

SARibbonToolButton{
  border:1px solid transparent;
  color:#FFFFFF;
  background-color:#2B2B2B;
  min-width:40px;
  font-size:12px;
  border-radius:4px;
  border-top-width:0px;
} 

SARibbonToolButton:pressed{
    color:#C6C6C6;
    background-color: #1E1E1E;
    border: 1px solid rgba(255,255,255,0.22);
}
SARibbonToolButton:checked{
    color:#C6C6C6;
    border: 1px solid rgba(255,255,255,0.22);
    background-color: #1E1E1E;
}
SARibbonToolButton:!checked:hover {
    color:#C6C6C6;
    background-color: rgba(255,255,255,0.12);
    border:1px solid rgba(255,255,255,0.12);
}
SARibbonToolButton:disabled{
  color:#808080;
}
/*
SARibbonPannel下的SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
*/
SARibbonPannel > SARibbonSeparatorWidget {
  color: #5d636a;
}

/*SARibbonPannelLabel Pannel下标题栏*/
SARibbonPannelLabel {
    background-color: #2B2B2B;
    color: rgba(173, 173, 173, 1);
}

/*SARibbonPannelOptionButton*/
SARibbonPannelOptionButton {
  border:none;
  background-color: transparent;
  color:#C6C6C6;
}

SARibbonPannelOptionButton:hover {
  background-color:#375d77;
}

/*SARibbonButtonGroupWidget*/
SARibbonButtonGroupWidget{
  background-color: transparent;
  color: #C6C6C6;
}

SARibbonPannel > SARibbonButtonGroupWidget {
  border: 1px solid #2a8db5;
  background-color: transparent;
}

/*SARibbonApplicationButton*/
SARibbonApplicationButton{
  color: #afafa6;
  border-top: 2px solid transparent;/*加上边框，否则选中后加入边框后会有1px的偏移*/
  border-radius: 0px;
  min-width: 60px;
  min-height: 18px;
  background-color: #404040;
  margin: 0px;
  padding: 0px;
  padding-top: -2px;
  font-size: 14px;
}

SARibbonApplicationButton:hover{
  background-color: #343434;
  border-color: #0072a3;
}
SARibbonApplicationButton:open {
  color: #C6C6C6;
  background-color: #2B2B2B;
  border-top-color:#0A8FE2;
}
SARibbonApplicationButton:pressed{
  color: #ffffff;
}

SARibbonApplicationButton:focus{
  outline: none;
}

SARibbonApplicationButton::menu-indicator {
  /*subcontrol-position: right;*/
  width:0px;
}

/*SARibbonTabBar*/
SARibbonTabBar{
    background-color: #404040;
}

SARibbonTabBar::tab {
  font-weight: normal;
  color: #afafa6;
  border:0px;
  border-top: 2px solid transparent;/*加上边框，否则选中后加入边框后会有1px的偏移*/
  background: #404040;
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 5px;
  margin-bottom: 0px;
  min-width: 60px;
  min-height: 30px;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: -6px;
  font-size: 14px;
}

SARibbonTabBar::tab:selected {
  color: #C6C6C6;
  background-color: #2B2B2B;
  border-top-color:#0A8FE2;
}

SARibbonTabBar::tab:hover:!selected {
  background-color: #343434;
  border-color: #0072a3;
}

SARibbonTabBar::tab:!selected {
  color: #afafa6;
}

/*SARibbonCheckBox*/
SARibbonCheckBox{
    color:#C6C6C6;
    background-color:transparent;
}

/*SARibbonControlToolButton*/

SARibbonControlToolButton {
  background-color: transparent;/*{ControlToolButton.BKColor}*/
  color: #C6C6C6;
  border: 1px solid transparent;
}

SARibbonControlToolButton:pressed {
  background-color: #1E1E1E;/*{ControlToolButton.BKColor:pressed}*/
}

SARibbonControlToolButton:checked {
  border: 1px solid #2a8db5;/*{ControlToolButton.BorderColor:checked}*/
  background-color: #1E1E1E;/*{ControlToolButton.BKColor:checked}*/
}

SARibbonControlToolButton:hover {
  color: #C6C6C6;
  border: 1px solid transparent;/*{ControlToolButton.BorderColor:hover}*/
  background-color: #1E1E1E;/*{ControlToolButton.BKColor:hover}*/
}

/*SARibbonControlButton*/
SARibbonControlButton{
  background-color:transparent;
  border: none;
  color:#C6C6C6;
}
SARibbonControlButton:pressed{
  background-color: #1E1E1E;
}
SARibbonControlButton:checked{
  border: 1px solid #2a8db5;
  background-color: #1E1E1E;
}
SARibbonControlButton:hover {
  border: 1px solid transparent;
  background-color: #404040;
 }

SARibbonControlButton#SARibbonGalleryButtonUp,#SARibbonGalleryButtonDown,#SARibbonGalleryButtonMore{
  background-color:#404040;
  border: 1px solid #764363;
  color: #C6C6C6;
}


/*SARibbonMenu*/
SARibbonMenu {
  color:#C6C6C6;
  background-color: #2B2B2B;
}
SARibbonMenu::item {
  padding: 5px;
  background-color: #404040;
}
SARibbonMenu::item:selected {
  border: 1px solid #2a8db5;
  background-color: #1E1E1E;
}
SARibbonMenu::item:hover {
    color:#C6C6C6;
    border: 1px solid transparent;
    background-color: #1E1E1E;
}
SARibbonMenu::icon{
  margin-left: 1px;
}

/*SARibbonGallery*/
SARibbonGallery {
  background-color: #2B2B2B;
  color: #C6C6C6;
  border: 1px solid #888888;
}

/*SARibbonGalleryGroup*/
SARibbonGalleryGroup {
  show-decoration-selected: 1;
  background-color: transparent;
  color: #C6C6C6;
}
SARibbonGalleryGroup::item {
  color: #C6C6C6;
}
SARibbonGalleryGroup::item:selected {
  background-color: #1E1E1E;
  border: 1px solid #2a8db5;
}
SARibbonGalleryGroup::item:hover {
  background-color: #1E1E1E;
  border: 1px solid transparent;
}


/*RibbonGalleryViewport*/

SARibbonGalleryViewport {
  background-color: #2B2B2B;
}

/*SARibbonLineEdit*/
SARibbonLineEdit {
  border:1px solid #2B2B2B;
  background-color: #2B2B2B;
  color: #C6C6C6;
  selection-background-color: #2a8db5;
  selection-color: #C6C6C6;
}


/*SARibbonComboBox*/
SARibbonComboBox {
  color : #C6C6C6;
  border: 1px solid #2B2B2B;
  background-color: #2B2B2B;
}

SARibbonComboBox:hover{
  border: 1px solid #2a8db5;
}

SARibbonComboBox:editable {
  color : #C6C6C6;
  background-color: #2B2B2B;
  selection-background-color: #2a8db5;
  selection-color: #C6C6C6;
}

SARibbonComboBox::drop-down {
  subcontrol-origin: padding;
  subcontrol-position: top right;
  width: 1em;
  border: none;
}

SARibbonComboBox::drop-down:hover {
  border: none;
  background-color: #2a8db5;
}

SARibbonComboBox::down-arrow {
  image: url(:/image/resource/ArrowDown.png);
}
/*SARibbonCategoryScrollButton*/
SARibbonCategoryScrollButton {
  border: 1px solid #2a8db5;
  color: #C6C6C6;
  background-color: #2B2B2B;
}

SARibbonCategoryScrollButton[arrowType="3"] {
  border-right-width: 1px;
}

SARibbonCategoryScrollButton[arrowType="4"] {
  border-left-width: 1px;
}
/*SARibbonSystemToolButton*/

SARibbonSystemToolButton {
  background-color: #2B2B2B;
  background-color: transparent;
  border:0;
}

SARibbonSystemToolButton:focus {
  outline: none;
}

/* 深色模式的系统按钮设置 */
/*Min*/
SARibbonSystemToolButton#SAMinimizeWindowButton {
  background-position:center;
  background-repeat: no-repeat;
  background-image: url(:/image/resource/Titlebar_Min_Hover.svg);
}
SARibbonSystemToolButton#SAMinimizeWindowButton:hover{
  background-color: #383838;
}
SARibbonSystemToolButton#SAMinimizeWindowButton:pressed{
  background-color: #383838;
}
/*Max*/
SARibbonSystemToolButton#SAMaximizeWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/image/resource/Titlebar_Max_Hover.svg);
}
SARibbonSystemToolButton#SAMaximizeWindowButton:hover {
    background-color: #383838;
}
SARibbonSystemToolButton#SAMaximizeWindowButton:checked {
  background-color: #383838;
  background-image: url(:/image/resource/Titlebar_Normal_Hover.svg) center;
}


/*Close*/
SARibbonSystemToolButton#SACloseWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/image/resource/Titlebar_Close_Hover.svg);
    background-color: #2B2B2B;
}
SARibbonSystemToolButton#SACloseWindowButton:hover {
  background-color: #383838;
}
SARibbonSystemToolButton#SACloseWindowButton:pressed {
    background-color: #383838;
}