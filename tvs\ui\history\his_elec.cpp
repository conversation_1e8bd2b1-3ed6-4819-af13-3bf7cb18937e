#include "qsplitter.h"
#include "ui/monitor/uimonitorstatus.h"
#include "ui/monitor/uidatatable.h"
#include "ui/history/his_elec.h"
#include "ui/monitor/uiwave.h"
#include "plot_widget.h"
#include "core/Graph.h"
#include "utils/kissfft/kiss_fft.h"

#include <MainWindow.h>
#include <QLabel>
#include <QVBoxLayout>
#include <QVector>
#include <QPair>
#include "utils/fft.h"
#include "app/settings.h"
#include "DockAreaWidget.h"
#include "DockManager.h"
#include "DockWidget.h"


UiHistoryElec::UiHistoryElec(const QString &id, QWidget *parent)
    : QWidget{parent}, m_id(id), m_firstRun(true), m_lastPointKey(0.0)
{
    setAttribute(Qt::WA_StyledBackground);
    QVBoxLayout *layout = new QVBoxLayout(this);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_main_wnd.margins");
    if (margins.size() == 4)
    {
        layout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto m_dock_manager = new ads::CDockManager(this);
    layout->addWidget(m_dock_manager);

    m_dock_manager->setDockWidgetToolBarStyle(Qt::ToolButtonIconOnly, ads::CDockWidget::StateFloating);

    auto v_splitter = new QSplitter(Qt::Vertical, this);
    v_splitter->setContentsMargins(0, 0, 0, 0);

    // 电压
    WaveConfig config = {"电压", "时间(s)", "电压/(kV"};
    m_uiWaveVoltage = new UiWave(config);

    // 频谱
    config = {"电压频谱", "频率/(Hz)", "幅值/(dB)"};
    m_uiWaveVoltageSpectrum = new UiWave(config);

    v_splitter->addWidget(m_uiWaveVoltage);
    v_splitter->addWidget(m_uiWaveVoltageSpectrum);


    auto viewVoltage = new CDockWidget("电压");
    viewVoltage->setFeature(CDockWidget::DockWidgetFloatable, false);
    viewVoltage->setFeature(CDockWidget::DockWidgetClosable, false);
    viewVoltage->setWidget(uiWaveTV);
    m_dock_manager->addDockWidget(DockWidgetArea::LeftAutoHideArea, viewVoltage);






//     // 中层：电流
//     auto h_splitter_current = new QSplitter(Qt::Horizontal, this);
//     h_splitter_current->setContentsMargins(0, 0, 0, 0);

//     // 速度
//     config = {"电流", "时间/(s)", "电流/(kA)"};
//     m_uiWaveCurrent = new UiWave(config);

//     // 速度频谱
//     config = {"频谱", "频率/(Hz)", "幅值/(dB)"};
//     m_uiWaveCurrentSpectrum = new UiWave(config);

//     h_splitter_current->addWidget(m_uiWaveCurrent);
//     h_splitter_current->addWidget(m_uiWaveCurrentSpectrum);

//     // 下层：励磁电流
//     config = {"励磁电流", "时间/(s)", "电流/(A)"};
//     m_uiWaveFieldCurrent  = new UiWave(config);

//     // 将三层添加到垂直分割器
//     v_splitter->addWidget(h_splitter_voltage);  // 上层
//     v_splitter->addWidget(h_splitter_current);    // 中层
//     v_splitter->addWidget(m_uiWaveFieldCurrent);       // 下层

//     // 设置三层的初始大小比例 (1:1:1)
//     int h = v_splitter->size().height();
//     v_splitter->setSizes({h / 3, h / 3, h / 3});

//     // 不允许折叠任何一层
//     v_splitter->setCollapsible(0, false);  // 上层
//     v_splitter->setCollapsible(1, false);  // 中层
//     v_splitter->setCollapsible(2, false);  // 下层

//     m_uiMonitorStatus = new UiMonitorStatus();
//     mainLayout->addWidget(m_uiMonitorStatus);

//     auto lb1 = new QLabel("开始时间: 00:00:00");
//     lb1->setAlignment(Qt::AlignCenter);
//     m_uiMonitorStatus->m_layout->addWidget(lb1, 1);

//     auto line = new QFrame();
//     line->setFrameShape(QFrame::VLine);

//     m_uiMonitorStatus->m_layout->addWidget(line);

//     auto txt = R"(
// <table>
//     <td align="left" valign="middle">
//         <img src="./res/icon/status/warn.png" width="16" height="16" style="vertical-align:middle">
//     </td>
//     <td align="left" valign="middle">设备异常告警:已断开连接</td>
// </table>
// )";

//     m_info = new QLabel(txt);
//     m_info->setAlignment(Qt::AlignCenter);
//     m_uiMonitorStatus->m_layout->addWidget(m_info);

//     connect(MainWindow::instance, &MainWindow::equipmentConnectChanged, [this](QString id, bool bconnect)
//     {
//         if( m_id == id )
//         {
//             QString txt = R"(
// <table>
//     <td align="left" valign="middle">
//         <img src="./res/icon/status/%1.png" width="16" height="16" style="vertical-align:middle">
//     </td>
//     <td align="left" valign="middle">%2</td>
// </table>
// )";

//             if(bconnect)
//             {
//                 txt = txt.arg("success", "PA设备已连接");
//             }
//             else
//             {
//                 txt = txt.arg("warn", "设备异常告警:已断开连接");
//             }

//             m_info->setText(txt);
//         } });

//     m_graph_voltage_a = new Graph(new DaqDataSource("A相电压"));
//     m_uiWaveVoltage->get_plot_widget()->addGraph(m_graph_voltage_a);
    
//     m_graph_voltage_b = new Graph(new DaqDataSource("B相电压"));
//     m_uiWaveVoltage->get_plot_widget()->addGraph(m_graph_voltage_b);
    
//     m_graph_voltage_c = new Graph(new DaqDataSource("C相电压"));
//     m_uiWaveVoltage->get_plot_widget()->addGraph(m_graph_voltage_c);


//     m_graph_voltage_spectrum_a = new Graph(new DaqDataSource("A相电压"));
//     m_uiWaveVoltageSpectrum->get_plot_widget()->addGraph(m_graph_voltage_spectrum_a);
    
//     m_graph_voltage_spectrum_b = new Graph(new DaqDataSource("B相电压"));
//     m_uiWaveVoltageSpectrum->get_plot_widget()->addGraph(m_graph_voltage_spectrum_b);
    
//     m_graph_voltage_spectrum_c = new Graph(new DaqDataSource("C相电压"));
//     m_uiWaveVoltageSpectrum->get_plot_widget()->addGraph(m_graph_voltage_spectrum_c);

//     // 电流
//     m_graph_current_a = new Graph(new DaqDataSource("A相电流"));
//     m_uiWaveCurrent->get_plot_widget()->addGraph(m_graph_current_a);
    
//     m_graph_current_b = new Graph(new DaqDataSource("B相电流"));
//     m_uiWaveCurrent->get_plot_widget()->addGraph(m_graph_current_b);
    
//     m_graph_current_c = new Graph(new DaqDataSource("C相电流"));
//     m_uiWaveCurrent->get_plot_widget()->addGraph(m_graph_current_c);

//     m_graph_current_spectrum_a = new Graph( new DaqDataSource("A相电流"));
//     m_uiWaveCurrentSpectrum->get_plot_widget()->addGraph(m_graph_current_spectrum_a);
    
//     m_graph_current_spectrum_b = new Graph( new DaqDataSource("B相电流"));
//     m_uiWaveCurrentSpectrum->get_plot_widget()->addGraph(m_graph_current_spectrum_b);
    
//     m_graph_current_spectrum_c = new Graph( new DaqDataSource("C相电流"));
//     m_uiWaveCurrentSpectrum->get_plot_widget()->addGraph(m_graph_current_spectrum_c);

//     m_graph_field_current = new Graph(new DaqDataSource("励磁电流"));
//     m_uiWaveFieldCurrent->get_plot_widget()->addGraph(m_graph_field_current);

//     // 设置Y轴范围
//     m_uiWaveVoltage->get_plot_widget()->getPlot()->yAxis->setRange(-1.1, 1.1);
//     m_uiWaveCurrent->get_plot_widget()->getPlot()->yAxis->setRange(-1.1, 1.1);
//     m_uiWaveFieldCurrent->get_plot_widget()->getPlot()->yAxis->setRange(-1.1, 1.1);

//     connect(&dataTimer, SIGNAL(timeout()), this, SLOT(timeToAddData()));
//     dataTimer.start(1000);


//     // connect(m_uiWaveVoltage->get_plot_widget(), SIGNAL(graphSelected(PlotWidget*, Graph*)), 
//     //         MainWindow::instance, SLOT(graphSelected(PlotWidget*, Graph*)));

//     connect(m_uiWaveVoltage->get_plot_widget(), &PlotWidget::graphSelected, MainWindow::instance, &MainWindow::graphSelected);


}

bool UiHistoryElec::isUiDataVisiable() const
{
    // return m_uiDataTable->isVisible();
    return true;
}

void UiHistoryElec::setUiDataVisiable(bool visible)
{
    // m_uiDataTable->setVisible(visible);
}

void UiHistoryElec::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 设置最小尺寸
    // m_splitter_top->setMinimumSize(100, event->size().height() / 5);
    // m_uiWavePulse->setMinimumSize(100, event->size().height() / 5);
    // m_uiDataTable->setMinimumSize(100, event->size().height()/5);
}



void UiHistoryElec::timeToAddData()
{
    if (m_firstRun)
    {
        m_dataTimer.start(); // 第一次运行时启动计时器
        m_firstRun = false;
    }

    double key = m_dataTimer.elapsed() / 1000.0; // 开始到现在的时间，单位秒

    double fsample = 1000;           // 采样率，单位Hz
    double interval = 1.0 / fsample; // 采样间隔，单位秒

    int n = std::ceil((key - m_lastPointKey) / interval);
    if (n == 0)
        return;

    // 生成角速度数据
    QVector<double> keys, angular_values;
    for (int i = 0; i < n; ++i)
    {
        keys.append(m_lastPointKey + i * interval);
        angular_values.append(qSin((m_lastPointKey + i * interval) * 30) );
    }

    // 生成速度数据（不同频率的正弦波）
    QVector<double> speed_values;
    for (int i = 0; i < n; ++i)
    {
        speed_values.append(0.8 * qSin((m_lastPointKey + i * interval) * 20) );
    }

    // 生成励磁电流数据（更高频率的正弦波）
    QVector<double> pulse_values;
    for (int i = 0; i < n; ++i)
    {
        pulse_values.append(0.6 * qSin((m_lastPointKey + i * interval) * 50) );
    }


    // 上层：角速度和角速度频谱
    m_uiWaveVoltage->get_plot_widget()->addGraphData(m_graph_voltage_a, keys, angular_values, true, 100000);

    QPair<QVector<double>, QVector<double>> angular_spectrum = computeSpectrum(angular_values, fsample);
    m_uiWaveVoltageSpectrum->get_plot_widget()->updateGraphData(m_graph_voltage_spectrum_a, angular_spectrum.first, angular_spectrum.second, true);
    m_uiWaveVoltageSpectrum->get_plot_widget()->getPlot()->autolimits();

    // 中层：速度和速度频谱
    m_uiWaveCurrent->get_plot_widget()->addGraphData(m_graph_current_a, keys, speed_values, true, 100000);

    QPair<QVector<double>, QVector<double>> speed_spectrum = computeSpectrum(speed_values, fsample);
    m_uiWaveCurrentSpectrum->get_plot_widget()->updateGraphData(m_graph_current_spectrum_a, speed_spectrum.first, speed_spectrum.second, true);
    m_uiWaveCurrentSpectrum->get_plot_widget()->getPlot()->autolimits();

    // 下层：励磁电流
    m_uiWaveFieldCurrent->get_plot_widget()->addGraphData(m_graph_field_current, keys, pulse_values, true, 100000);

    // 记录当前时刻
    m_lastPointKey = key;

}
