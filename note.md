## 去掉 .toInt()
(YamlConfigUI::instance\(\)\.getValue<int>\([^)]+\))\.toInt\(\)
$1


## / 替换为 .
QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_pj_new.margins");
QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_pj_new.margins");

(YamlConfigUI::instance\(\)\.getArray<int>\(")([^"]+)\/([^"]+)("\))
$1$2.$3$4

(YamlConfigUI::instance\(\)\.getValue<int>\(")([^"]+)\/([^"]+)("\))
$1$2.$3$4