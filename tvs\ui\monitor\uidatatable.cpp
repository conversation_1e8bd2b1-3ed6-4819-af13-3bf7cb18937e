#include "uidatatable.h"
#include "qheaderview.h"
#include "widgets/stdwidget.h"

#include <QLabel>
#include <QStackedWidget>
#include <QTableWidget>
#include <QToolBar>
#include <QVBoxLayout>
#include <mainwindow.h>

#include <app/settings.h>

#include <utils/actionmanager.h>
#include <Qsci/qsciscintilla.h>
#include <Qsci/qscilexerpython.h>
#include <Qsci/qscilexercpp.h>
#include <Qsci/qsciapis.h>
#include "widgets/codewidget/codewidget.h"


UiDataTable::UiDataTable(QWidget *parent)
    : QWidget{parent}
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_toolbar_pane.margins");
    if (margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto toolBar = new QToolBar();
    mainLayout->addWidget(toolBar);

    QVBoxLayout *paneLayout = new QVBoxLayout();
    mainLayout->addLayout(paneLayout, 1);

    QVector<int> pane_margins = YamlConfigUI::instance().getArray<int>("ui_pane.margins");
    if (pane_margins.size() == 4)
    {
        paneLayout->setContentsMargins(pane_margins[0], pane_margins[1], pane_margins[2], pane_margins[3]);
    }

    toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    toolBar->addWidget(new QLabel("数值视图"));

    QWidget *blank = new QWidget();
    blank->setObjectName("Blank");
    blank->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    toolBar->addWidget(blank);

    QAction *hide = toolBar->addAction(QIcon("./res/icon/widget/hide.png"), "");
    connect(hide, &QAction::triggered, [this]()
            {
        this->hide();
        MainWindow::instance->setActionChecked("数值视图", false); });

    // tabbar
    auto tabbar = new GroupButton();

    tabbar->addTab("常规数据");
    tabbar->addTab("积分数据");
    tabbar->addTab("谐波指标");
    tabbar->addTab("谐波列表");
    tabbar->addTab("用户自定义");

    tabbar->setCurrentIndex(0);
    tabbar->setSizePolicy(QSizePolicy(QSizePolicy::Maximum, QSizePolicy::Maximum));

    paneLayout->addWidget(tabbar);

    // stackedWidget
    auto stackedWidget = new QStackedWidget();
    stackedWidget->setCurrentIndex(0);
    paneLayout->addWidget(stackedWidget, 1);

    connect(tabbar, SIGNAL(currentChanged(int)), stackedWidget, SLOT(setCurrentIndex(int)));

    ///////////////////////////////////////////////////////////////////////////////////////
    // tb_common
    QTableWidget *tb_common = new QTableWidget;

    // 隐藏表头
    tb_common->verticalHeader()->setVisible(false);
    tb_common->horizontalHeader()->setVisible(false);

    // 整行选中
    tb_common->setSelectionBehavior(QAbstractItemView::SelectRows);
    // 不可编辑
    tb_common->setEditTriggers(QAbstractItemView::NoEditTriggers);
    // 单选
    tb_common->setSelectionMode(QAbstractItemView::SingleSelection);
    // 开启隔行背景颜色, alternate-background-color: rgb(218, 233, 231);
    tb_common->setAlternatingRowColors(true);

    QVector<QVector<QString>> tb1_data =
        {
            {"功率数据", "V", "A", "V", "A", "W", "var", "VA", "Hz", "Hz", "λ"},
            {"机械数据", "rpm", "Nm", "°", "W", "rpm", "%", "", "", "", ""},
            {"其他数据", "η1", "η2", "η3", "Udef1", "Udef2", "F1", "F2", "F3", "F4", "F5"},

        };

    tb_common->setRowCount(tb1_data.size());
    tb_common->setColumnCount(tb1_data[0].size());

    // 设置表头
    // QStringList headers;
    // for (int col = 0; col < data[0].size(); ++col) {
    //     headers << QString("Column %1").arg(col + 1);
    // }
    // tb_common->setHorizontalHeaderLabels(headers);

    // 填充表格数据
    for (int row = 0; row < tb1_data.size(); ++row)
    {
        for (int col = 0; col < tb1_data[row].size(); ++col)
        {
            QTableWidgetItem *item = new QTableWidgetItem(tb1_data[row][col]);
            tb_common->setItem(row, col, item);
        }
    }

    stackedWidget->addWidget(tb_common);

    ///////////////////////////////////////////////////////////////////////////////////////
    // 其他表格
    auto tabel1 = new QLabel("");
    tabel1->setStyleSheet("background-color: lightyellow;");
    stackedWidget->addWidget(tabel1);

    auto tabel2 = new QLabel("");
    tabel2->setStyleSheet("background-color: lightblue;");
    stackedWidget->addWidget(tabel2);

    auto tabel3 = new QLabel("");
    tabel3->setStyleSheet("background-color: lightgreen;");
    stackedWidget->addWidget(tabel3);

    // QsciScintilla *editor = createPythonEditor(nullptr);
    CodeWidget *editor = new CodeWidget(nullptr);
    QString le ("py");
    editor->setCurLexer(le);

    
    stackedWidget->addWidget(editor);
}
